#!/usr/bin/env python3
"""
Comprehensive Schema Export Tool - Modular Package

A professional-grade database schema export tool with AI-focused analysis capabilities.
Designed for enterprise environments with advanced features for data quality analysis,
relationship detection, and comprehensive schema documentation.

Main Components:
- ConfigManager: Configuration handling and validation
- DatabaseConnector: Database connection management with fallbacks
- QueryGenerator: SQL query generation for schema information
- AIAnalyzer: AI-focused analysis of database schema and data
- DataQualityAnalyzer: Data quality analysis and issue detection
- ExportWriter: Output formatting and file writing
- SchemaExporter: Main orchestration class

Usage:
    from comprehensive_schema_export import SchemaExporter
    
    # Basic usage
    exporter = SchemaExporter()
    schema_data = exporter.export_schema()
    files = exporter.save_exports()
    
    # From config file
    exporter = SchemaExporter.from_config_file('config.json')
    schema_data = exporter.export_schema()
    files = exporter.save_exports()
"""

from .schema_exporter import SchemaExporter
from .config_manager import ConfigManager
from .database_connector import DatabaseConnector
from .query_generator import QueryGenerator
from .ai_analyzer import AIAnalyzer
from .data_quality_analyzer import DataQualityAnalyzer
from .export_writer import ExportWriter
from .exceptions import (
    SchemaExportError,
    DatabaseConnectionError,
    QueryExecutionError,
    ConfigurationError,
    ExportWriterError,
    AIAnalysisError
)

# Version information
__version__ = "3.0.0"
__author__ = "Comprehensive Schema Export Tool"
__description__ = "Professional database schema export tool with AI analysis capabilities"

# Main exports
__all__ = [
    # Main class
    'SchemaExporter',
    
    # Core components
    'ConfigManager',
    'DatabaseConnector', 
    'QueryGenerator',
    'AIAnalyzer',
    'DataQualityAnalyzer',
    'ExportWriter',
    
    # Exceptions
    'SchemaExportError',
    'DatabaseConnectionError',
    'QueryExecutionError',
    'ConfigurationError',
    'ExportWriterError',
    'AIAnalysisError',
    
    # Version info
    '__version__',
    '__author__',
    '__description__'
]


def create_exporter(config=None):
    """Convenience function to create a SchemaExporter instance"""
    return SchemaExporter(config)


def create_exporter_from_config(config_path):
    """Convenience function to create a SchemaExporter from config file"""
    return SchemaExporter.from_config_file(config_path)


# Module-level convenience functions
def export_schema(config=None):
    """Quick export function for simple use cases"""
    exporter = SchemaExporter(config)
    return exporter.export_schema()


def export_and_save(config=None, base_filename=None):
    """Quick export and save function for simple use cases"""
    exporter = SchemaExporter(config)
    schema_data = exporter.export_schema()
    files = exporter.save_exports(base_filename)
    return schema_data, files
