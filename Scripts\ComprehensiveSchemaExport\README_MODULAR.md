# Comprehensive Database Schema Export Tool v3.0 (Modular)

A professional-grade, modular database schema export tool designed specifically for AI agent consumption. This tool provides comprehensive database schema information with advanced analysis capabilities, data quality assessment, and intelligent metadata generation.

## 🏗️ **Modular Architecture (v3.0)**

The tool has been completely refactored into a clean, modular architecture for better maintainability, testability, and extensibility:

### **Core Modules:**
- **`config_manager.py`** - Configuration handling and validation
- **`database_connector.py`** - Database connection management with fallbacks
- **`query_generator.py`** - SQL query generation for schema information
- **`ai_analyzer.py`** - AI-focused analysis of database schema and data
- **`data_quality_analyzer.py`** - Data quality analysis and issue detection
- **`export_writer.py`** - Output formatting and file writing
- **`schema_exporter.py`** - Main orchestration class
- **`utils.py`** - Utility functions and helpers
- **`exceptions.py`** - Custom exception classes

## 🚀 **Quick Start**

### **Simple Usage (New Modular API)**
```python
from comprehensive_schema_export import SchemaExporter

# Basic usage with default configuration
exporter = SchemaExporter()
schema_data = exporter.export_schema()
files = exporter.save_exports()

# From configuration file
exporter = SchemaExporter.from_config_file('config.json')
schema_data = exporter.export_schema()
files = exporter.save_exports()

# With custom configuration
config = {
    'server': 'localhost\\SQLEXPRESS',
    'database': 'MyDatabase',
    'output_formats': ['json'],
    'schema_filter_mode': 'exclude',
    'excluded_schemas': ['sys', 'temp']
}
exporter = SchemaExporter(config)
schema_data = exporter.export_schema()
files = exporter.save_exports()
```

### **Command Line Usage**
```bash
# Basic usage
python main.py

# With configuration file
python main.py config.json

# With command line options
python main.py --output json --sample-size 20 --no-backup

# Show help
python main.py --help

# Show environment information
python main.py --env-info
```

### **Organized Directory Structure**
The tool automatically creates an organized directory structure:

```
ComprehensiveSchemaExport/
├── 📁 exports/                    # All export files organized by timestamp
│   ├── 📂 20240115_143022/        # Export session folder
│   │   ├── CompleteViewVms_schema_export_20240115_143022.json
│   │   ├── CompleteViewVms_schema_export_20240115_143022.xml
│   │   └── CompleteViewVms_schema_export_20240115_143022.txt
│   └── 📂 20240115_151245/        # Another export session
├── 📁 logs/                       # Daily log files
│   ├── schema_export_20240115.log
│   └── schema_export_20240116.log
├── 📁 Core Modules/               # Modular components
│   ├── config_manager.py
│   ├── database_connector.py
│   ├── query_generator.py
│   ├── ai_analyzer.py
│   ├── data_quality_analyzer.py
│   ├── export_writer.py
│   ├── schema_exporter.py
│   ├── utils.py
│   └── exceptions.py
├── main.py                        # Command line interface
├── comprehensive_schema_export.py # Legacy compatibility
└── README_MODULAR.md             # This file
```

## 🎯 **Key Features**

### **📊 Comprehensive Schema Analysis**
- **Complete database structure** - Tables, columns, relationships, constraints
- **Advanced SQL Server features** - Sequences, synonyms, temporal tables, memory-optimized tables
- **Stored procedures and functions** with full definitions
- **Views and triggers** with source code
- **Extended properties** and database documentation

### **🤖 AI-Focused Analysis**
- **Pattern detection** in data (emails, phones, URLs, GUIDs)
- **Relationship inference** beyond explicit foreign keys
- **Table classification** (lookup, fact, bridge, audit tables)
- **Column semantics** analysis for intelligent field mapping
- **Query pattern suggestions** for common operations

### **🔍 Data Quality Assessment**
- **High null percentage** column identification
- **Date range anomalies** detection (future dates, defaults)
- **Orphaned records** analysis
- **Referential integrity** issue detection
- **Duplicate value concerns** flagging

### **🔧 Modular Configuration**
```python
# Using individual components
from comprehensive_schema_export import ConfigManager, DatabaseConnector, QueryGenerator

config_manager = ConfigManager.from_file('config.json')
db_connector = DatabaseConnector(config_manager)
query_generator = QueryGenerator(config_manager)

# Test connection
if db_connector.test_connection():
    print("Database connection successful!")

# Get queries
queries = query_generator.get_comprehensive_queries()
```

## 📋 **Configuration Examples**

### **Basic Configuration**
```json
{
  "server": "localhost\\SQLEXPRESS",
  "database": "MyDatabase",
  "username": "sa",
  "password": "password",
  "output_formats": ["json", "xml", "txt"],
  "sample_size": 10,
  "backup_to_desktop": true
}
```

### **Enterprise Configuration**
```json
{
  "server": "prod-server\\instance",
  "database": "ProductionDB",
  "schema_filter_mode": "exclude",
  "excluded_schemas": [
    "sys", "INFORMATION_SCHEMA", "guest",
    "HangFire", "Elmah", "NLog", "backup", "temp"
  ],
  "max_tables_to_sample": 50,
  "max_table_rows_for_sampling": 500000,
  "include_system_stats": true,
  "analyze_column_patterns": true,
  "generate_ai_hints": true
}
```

## 🔧 **Advanced Usage**

### **Custom AI Analysis**
```python
from comprehensive_schema_export import AIAnalyzer, ConfigManager, DatabaseConnector

config_manager = ConfigManager()
db_connector = DatabaseConnector(config_manager)
ai_analyzer = AIAnalyzer(config_manager, db_connector)

with db_connector.get_connection() as conn:
    cursor = conn.cursor()
    
    # Analyze specific table patterns
    patterns = ai_analyzer.analyze_column_patterns(cursor, 'Users', 'Email', 'varchar')
    
    # Get column statistics
    stats = ai_analyzer.get_column_statistics(cursor, 'Orders', 'OrderDate')
    
    # Detect relationships
    relationships = ai_analyzer.detect_table_relationships(cursor)
```

### **Data Quality Analysis**
```python
from comprehensive_schema_export import DataQualityAnalyzer

quality_analyzer = DataQualityAnalyzer(config_manager, db_connector)

with db_connector.get_connection() as conn:
    cursor = conn.cursor()
    table_details = {}  # Your table details
    
    quality_issues = quality_analyzer.analyze_data_quality(cursor, table_details)
    
    # Check specific issues
    high_null_columns = quality_issues['high_null_percentage_columns']
    orphaned_records = quality_issues['potential_orphaned_records']
```

## 🛠️ **Benefits of Modular Architecture**

### **For Developers:**
- **Single Responsibility** - Each module has a clear, focused purpose
- **Easy Testing** - Individual components can be tested in isolation
- **Maintainability** - Changes to one module don't affect others
- **Extensibility** - New features can be added as separate modules

### **For Users:**
- **Flexible Usage** - Use only the components you need
- **Better Performance** - Load only required functionality
- **Cleaner Code** - Well-organized, professional structure
- **Future-Proof** - Easier to upgrade and extend

### **For AI Agents:**
- **Modular Integration** - Import specific analyzers as needed
- **Custom Workflows** - Build custom analysis pipelines
- **Component Reuse** - Use individual modules in other projects
- **Clear Interfaces** - Well-defined APIs for each component

## 📦 **Installation & Dependencies**

```bash
# Install required packages
pip install pyodbc

# For development
pip install pytest pytest-cov black flake8
```

## 🧪 **Testing**

```bash
# Run tests (when test suite is added)
python -m pytest tests/

# Test individual components
python -c "from comprehensive_schema_export import ConfigManager; print('Config manager works!')"
python -c "from comprehensive_schema_export import DatabaseConnector; print('Database connector works!')"
```

## 🔄 **Migration from v2.x**

The modular version maintains backward compatibility:

```python
# Old way (still works)
from comprehensive_schema_export import ComprehensiveSchemaExporter
exporter = ComprehensiveSchemaExporter()

# New way (recommended)
from comprehensive_schema_export import SchemaExporter
exporter = SchemaExporter()
```

## 📈 **Version History**

- **v3.0** - Modular architecture, improved maintainability
- **v2.2** - Added advanced SQL Server features (sequences, temporal tables)
- **v2.1** - AI analysis and data quality features
- **v2.0** - Schema filtering and performance optimizations
- **v1.0** - Initial comprehensive schema export

The modular architecture makes the tool more professional, maintainable, and suitable for enterprise environments! 🚀
