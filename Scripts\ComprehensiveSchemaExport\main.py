#!/usr/bin/env python3
"""
Main entry point for the Comprehensive Schema Export Tool

This script provides a simple interface to the modular schema export tool.
It maintains backward compatibility while using the new modular architecture.

Usage:
    python main.py                          # Use default configuration
    python main.py config.json             # Use configuration file
    python main.py --help                  # Show help information

Author: Comprehensive Schema Export Tool
Version: 3.0 (Modular)
"""

import sys
import argparse
import json
import logging
from pathlib import Path

# Import the modular components
try:
    from .schema_exporter import SchemaExporter
    from .config_manager import ConfigManager
    from .exceptions import SchemaExportError, ConfigurationError
    from .utils import setup_logging, get_environment_info
except ImportError:
    # Fallback for direct execution
    from schema_exporter import SchemaExporter
    from config_manager import ConfigManager
    from exceptions import SchemaExportError, ConfigurationError
    from utils import setup_logging, get_environment_info

# Directory constants
SCRIPT_DIR = Path(__file__).parent.absolute()
LOGS_DIR = SCRIPT_DIR / "logs"

# Setup logging
setup_logging(LOGS_DIR)
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Comprehensive Database Schema Export Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                          # Use default configuration
  python main.py config.json             # Use configuration file
  python main.py --output json           # Export only JSON format
  python main.py --sample-size 20        # Use 20 sample rows per table
  python main.py --no-backup             # Don't create desktop backups
  python main.py --schema-mode include   # Include only specified schemas
  python main.py --help                  # Show this help message

Configuration:
  The tool can be configured using environment variables:
    DB_SERVER, DB_DATABASE, DB_USERNAME, DB_PASSWORD
  
  Or by providing a JSON configuration file as the first argument.
        """
    )
    
    parser.add_argument(
        'config_file',
        nargs='?',
        help='JSON configuration file path (optional)'
    )
    
    parser.add_argument(
        '--output',
        choices=['json', 'xml', 'txt', 'all'],
        default='all',
        help='Output format (default: all)'
    )
    
    parser.add_argument(
        '--sample-size',
        type=int,
        default=10,
        help='Number of sample rows per table (default: 10)'
    )
    
    parser.add_argument(
        '--no-backup',
        action='store_true',
        help='Don\'t create desktop backups'
    )
    
    parser.add_argument(
        '--schema-mode',
        choices=['exclude', 'include', 'all'],
        default='exclude',
        help='Schema filtering mode (default: exclude)'
    )
    
    parser.add_argument(
        '--max-tables',
        type=int,
        default=20,
        help='Maximum number of tables to process (default: 20)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Comprehensive Schema Export Tool v3.0'
    )
    
    parser.add_argument(
        '--env-info',
        action='store_true',
        help='Show environment information and exit'
    )
    
    return parser.parse_args()


def create_config_from_args(args):
    """Create configuration dictionary from command line arguments"""
    config = {}
    
    # Output format
    if args.output == 'all':
        config['output_formats'] = ['json', 'xml', 'txt']
    else:
        config['output_formats'] = [args.output]
    
    # Other settings
    config['sample_size'] = args.sample_size
    config['backup_to_desktop'] = not args.no_backup
    config['schema_filter_mode'] = args.schema_mode
    config['max_tables_to_sample'] = args.max_tables
    
    return config


def main():
    """Main entry point"""
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Setup verbose logging if requested
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        # Show environment info if requested
        if args.env_info:
            env_info = get_environment_info()
            print("Environment Information:")
            print("=" * 40)
            for key, value in env_info.items():
                print(f"{key}: {value}")
            return 0
        
        # Load configuration
        config = None
        if args.config_file:
            logger.info(f"Loading configuration from: {args.config_file}")
            try:
                config_manager = ConfigManager.from_file(args.config_file)
                config = config_manager.to_dict()
            except (ConfigurationError, FileNotFoundError) as e:
                logger.error(f"Failed to load configuration file: {e}")
                return 1
        
        # Apply command line overrides
        cli_config = create_config_from_args(args)
        if config:
            config.update(cli_config)
        else:
            config = cli_config
        
        # Create and run exporter
        logger.info("Initializing schema exporter...")
        exporter = SchemaExporter(config)
        
        # Export schema
        logger.info("Starting schema export...")
        schema_data = exporter.export_schema()
        
        # Save exports
        logger.info("Saving exports...")
        saved_files = exporter.save_exports()
        
        logger.info("Schema export completed successfully!")
        return 0
        
    except KeyboardInterrupt:
        logger.info("Export cancelled by user")
        return 1
    except SchemaExportError as e:
        logger.error(f"Schema export failed: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.debug("Full traceback:", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())
