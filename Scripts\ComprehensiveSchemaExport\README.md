# Comprehensive SQL Server Schema Export Tool

A professional-grade tool for exporting complete SQL Server database schema information optimized for AI agent consumption and advanced query building.

## Features

### 🔍 **Comprehensive Schema Information**
- **Enhanced table and column details** with data types, constraints, and relationships
- **Foreign key relationships** with referential actions (CASCADE, SET NULL, etc.)
- **Index information** including type, uniqueness, and column details
- **Views and stored procedures** with full definitions
- **Triggers** with definitions and status
- **Check constraints** and default values
- **User-defined data types**
- **Extended properties** (descriptions/comments)
- **Object dependencies** and relationships

### 📊 **Data Analysis & Sampling**
- **Sample data** from each table for context understanding
- **Column statistics** including null percentages and uniqueness ratios
- **Distinct value analysis** for categorical columns
- **Data distribution patterns**
- **Table size and space usage statistics**

### 🛡️ **Enterprise-Grade Reliability**
- **Multiple ODBC driver fallback** (17, 13, Native Client 11.0, SQL Server)
- **Connection retry logic** with exponential backoff
- **TCP connectivity pre-checks**
- **Comprehensive error handling** and logging
- **Resource management** with proper connection cleanup
- **Module dependency validation**

### 🔧 **Flexible Configuration**
- **Environment variable support** for secure credential management
- **Multiple output formats**: JSON, XML, and human-readable text
- **Configurable sample sizes** and data limits
- **Automatic desktop backups**
- **Customizable connection timeouts and retry settings**
- **Advanced schema filtering** for multi-vendor and enterprise environments

## Installation

### Prerequisites
```bash
pip install pyodbc
```

### ODBC Driver Requirements
The tool supports multiple SQL Server ODBC drivers with automatic fallback:
1. **ODBC Driver 17 for SQL Server** (recommended)
2. **ODBC Driver 13 for SQL Server**
3. **SQL Server Native Client 11.0**
4. **SQL Server** (basic driver)

## Quick Start

### Basic Usage
```python
from comprehensive_schema_export import ComprehensiveSchemaExporter

# Create exporter with default settings
exporter = ComprehensiveSchemaExporter()

# Export schema
schema_data = exporter.export_schema()

# Save in all formats (JSON, XML, TXT)
files = exporter.save_exports()

print(f"Export completed! Files: {files}")
```

### Organized Directory Structure
The tool automatically creates an organized directory structure:

```
ComprehensiveSchemaExport/
├── 📁 exports/                    # All export files organized by timestamp
│   ├── 📂 20240115_143022/        # Export session folder
│   │   ├── CompleteViewVms_schema_export_20240115_143022.json
│   │   ├── CompleteViewVms_schema_export_20240115_143022.xml
│   │   └── CompleteViewVms_schema_export_20240115_143022.txt
│   └── 📂 20240115_151245/        # Another export session
├── 📁 logs/                       # Daily log files
│   ├── schema_export_20240115.log
│   └── schema_export_20240116.log
├── comprehensive_schema_export.py # Main tool
├── config_examples.json          # Configuration examples
└── README.md                     # This file
```

**Benefits:**
- ✅ **Organized by timestamp** - Easy to find specific exports
- ✅ **Separate logs directory** - Clean log file management
- ✅ **No clutter** - Keeps the main directory clean
- ✅ **Desktop backups** - Optional copies to user's Desktop
- ✅ **Daily log rotation** - One log file per day

### Using Environment Variables (Recommended)
```bash
# Set environment variables for security
set DB_SERVER=your-server\instance
set DB_DATABASE=your-database
set DB_USERNAME=your-username
set DB_PASSWORD=your-password
```

```python
# Exporter automatically uses environment variables
exporter = ComprehensiveSchemaExporter()
schema_data = exporter.export_schema()
files = exporter.save_exports("secure_export")
```

### PowerShell Usage with Schema Filtering

```powershell
# Default - exclude system schemas
.\Run-SchemaExport.ps1

# Include only specific schemas
.\Run-SchemaExport.ps1 -IncludeSchemas "dbo,app,business" -SchemaFilterMode include

# Exclude additional vendor schemas
.\Run-SchemaExport.ps1 -ExcludeSchemas "sys,backup,temp,HangFire,Elmah" -SchemaFilterMode exclude

# Include all schemas (development environment)
.\Run-SchemaExport.ps1 -SchemaFilterMode all

# Enterprise multi-vendor environment
.\Run-SchemaExport.ps1 -ExcludeSchemas "sys,INFORMATION_SCHEMA,HangFire,Elmah,NLog,Telerik,DevExpress,backup,temp,staging" -OutputFormat json
```

## Configuration Options

```python
config = {
    'server': 'localhost\\SQLEXPRESS',
    'database': 'YourDatabase',
    'username': 'sa',
    'password': 'your-password',
    'drivers': [
        'ODBC Driver 17 for SQL Server',
        'ODBC Driver 13 for SQL Server',
        'SQL Server Native Client 11.0',
        'SQL Server'
    ],
    'connection_timeout': 30,
    'command_timeout': 300,
    'max_retries': 3,
    'retry_delay': 2,
    'output_formats': ['json', 'xml', 'txt'],
    'sample_size': 10,
    'max_distinct_values': 50,
    'backup_to_desktop': True,

    # Schema filtering options
    'excluded_schemas': [
        'sys', 'INFORMATION_SCHEMA', 'guest', 'backup', 'temp'
    ],
    'included_schemas': [],  # If specified, only these schemas
    'schema_filter_mode': 'exclude'  # 'exclude', 'include', or 'all'
}

exporter = ComprehensiveSchemaExporter(config)
```

### Schema Filtering Examples

```python
# Exclude system and vendor schemas (default)
config = {
    'excluded_schemas': [
        'sys', 'INFORMATION_SCHEMA', 'guest',
        'HangFire', 'Elmah', 'NLog', 'backup', 'temp'
    ],
    'schema_filter_mode': 'exclude'
}

# Include only specific application schemas
config = {
    'included_schemas': ['dbo', 'app', 'business', 'reporting'],
    'schema_filter_mode': 'include'
}

# Include everything (development/analysis)
config = {
    'schema_filter_mode': 'all'
}
```

## Output Formats

### JSON Format (AI-Optimized)
Perfect for AI agent consumption with structured data:
```json
{
  "export_metadata": {
    "export_timestamp": "2024-01-15T10:30:00",
    "database_name": "CompleteViewVms",
    "total_tables": 25
  },
  "schema_information": {
    "Enhanced Tables and Columns": [...],
    "Enhanced Foreign Key Relationships": [...],
    "Enhanced Index Information": [...]
  },
  "table_details": {
    "dbo.Users": {
      "sample_data": [...],
      "columns": {
        "UserID": {
          "data_type": "int",
          "statistics": {
            "total_rows": 1000,
            "null_percentage": 0,
            "distinct_count": 1000,
            "uniqueness_ratio": 100
          },
          "distinct_values": []
        }
      }
    }
  }
}
```

### XML Format
Structured XML for integration with other tools.

### Text Format
Human-readable format for documentation and review.

## Schema Information Exported

### Core Database Objects
- ✅ **Tables** with full column details, data types, and constraints
- ✅ **Views** with complete definitions
- ✅ **Stored Procedures** and **Functions** with source code
- ✅ **Triggers** with definitions and status
- ✅ **User-Defined Data Types**

### Relationships & Constraints
- ✅ **Primary Keys** and **Foreign Keys** with referential actions
- ✅ **Unique Constraints** and **Check Constraints**
- ✅ **Default Constraints** and **Computed Columns**
- ✅ **Index Information** with column details and properties

### Data Analysis
- ✅ **Sample Data** from each table (configurable size)
- ✅ **Column Statistics** (null counts, distinct values, uniqueness)
- ✅ **Categorical Value Analysis** (top values with frequencies)
- ✅ **Table Size Statistics** (row counts, space usage)

### Metadata & Documentation
- ✅ **Extended Properties** (MS_Description comments)
- ✅ **Object Dependencies** (what references what)
- ✅ **Database Information** (version, collation, compatibility)
- ✅ **Export Metadata** (timestamp, version, configuration)

## AI Agent Benefits

This tool is specifically designed to help AI agents understand databases by providing:

1. **Complete Context**: Full schema with relationships and constraints
2. **Data Patterns**: Sample data and statistics for understanding content
3. **Business Logic**: Stored procedures, triggers, and check constraints
4. **Semantic Information**: Comments and extended properties
5. **Usage Patterns**: Object dependencies and relationships

## Error Handling & Resilience

- **Connection Fallback**: Automatically tries multiple ODBC drivers
- **Retry Logic**: Configurable retry attempts with delays
- **Graceful Degradation**: Continues export even if some queries fail
- **Comprehensive Logging**: Detailed logs for troubleshooting
- **Resource Cleanup**: Proper connection and file handle management

## Examples

See `schema_export_example.py` for detailed usage examples including:
- Basic usage
- Custom configuration
- Environment variable setup
- AI-optimized exports

## Troubleshooting

### Common Issues

**Connection Failed**: Check ODBC drivers and connection string
**Permission Denied**: Ensure database user has sufficient permissions
**Timeout Errors**: Increase `connection_timeout` and `command_timeout`
**Large Database**: Reduce `sample_size` and `max_distinct_values`

### Logging
Check `schema_export.log` for detailed execution information and error messages.

## Security Best Practices

1. **Use Environment Variables** for credentials
2. **Limit Database Permissions** to read-only access
3. **Secure Output Files** containing sensitive schema information
4. **Regular Credential Rotation** for database accounts

---

**Version**: 2.0  
**Author**: Comprehensive Schema Export Tool  
**License**: Internal Use
