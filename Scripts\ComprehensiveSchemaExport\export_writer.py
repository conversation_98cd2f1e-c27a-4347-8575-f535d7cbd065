#!/usr/bin/env python3
"""
Export writing functionality for the Comprehensive Schema Export Tool
"""

import json
import xml.etree.ElementTree as ET
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
from .config_manager import ConfigManager
from .exceptions import ExportWriterError

logger = logging.getLogger(__name__)

# Get script directory for organized file structure
SCRIPT_DIR = Path(__file__).parent.absolute()
EXPORTS_DIR = SCRIPT_DIR / "exports"
LOGS_DIR = SCRIPT_DIR / "logs"

# Create directories if they don't exist
EXPORTS_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)


class ExportWriter:
    """Handles writing schema exports in multiple formats"""
    
    def __init__(self, config_manager: ConfigManager):
        """Initialize export writer"""
        self.config = config_manager
        self.schema_data = None
        self.export_timestamp = None
    
    def set_schema_data(self, schema_data: Dict[str, Any], export_timestamp: datetime) -> None:
        """Set the schema data to be exported"""
        self.schema_data = schema_data
        self.export_timestamp = export_timestamp
    
    def _save_json_format(self, filepath: Path) -> None:
        """Save schema data in JSON format"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.schema_data, f, indent=2, default=str, ensure_ascii=False)
            logger.info(f"✓ JSON export saved: {filepath}")
        except Exception as e:
            logger.error(f"✗ Failed to save JSON format: {e}")
            raise ExportWriterError(f"Failed to save JSON format: {e}")
    
    def _save_xml_format(self, filepath: Path) -> None:
        """Save schema data in XML format"""
        try:
            root = ET.Element("DatabaseSchema")
            
            # Add metadata
            metadata = ET.SubElement(root, "Metadata")
            for key, value in self.schema_data['export_metadata'].items():
                elem = ET.SubElement(metadata, key.replace('_', ''))
                elem.text = str(value)
            
            # Add schema information
            schema_info = ET.SubElement(root, "SchemaInformation")
            for section_name, data in self.schema_data['schema_information'].items():
                section = ET.SubElement(schema_info, section_name.replace(' ', '').replace('_', ''))
                if isinstance(data, list):
                    for item in data:
                        item_elem = ET.SubElement(section, "Item")
                        if isinstance(item, dict):
                            for key, value in item.items():
                                field = ET.SubElement(item_elem, key.replace(' ', '').replace('_', ''))
                                field.text = str(value) if value is not None else ''
                        else:
                            item_elem.text = str(item)
            
            # Write XML file
            tree = ET.ElementTree(root)
            tree.write(filepath, encoding='utf-8', xml_declaration=True)
            logger.info(f"✓ XML export saved: {filepath}")
        except Exception as e:
            logger.error(f"✗ Failed to save XML format: {e}")
            raise ExportWriterError(f"Failed to save XML format: {e}")
    
    def _save_text_format(self, filepath: Path) -> None:
        """Save schema data in human-readable text format"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                # Write header
                f.write("=" * 80 + "\n")
                f.write("COMPREHENSIVE DATABASE SCHEMA EXPORT\n")
                f.write("=" * 80 + "\n\n")
                
                # Write metadata
                f.write("EXPORT METADATA:\n")
                f.write("-" * 40 + "\n")
                for key, value in self.schema_data['export_metadata'].items():
                    f.write(f"{key.replace('_', ' ').title()}: {value}\n")
                f.write("\n")
                
                # Write schema information
                for section_name, data in self.schema_data['schema_information'].items():
                    f.write(f"\n{section_name.upper()}:\n")
                    f.write("-" * len(section_name) + "\n")
                    
                    if data:
                        if isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict):
                                    for key, value in item.items():
                                        f.write(f"  {key}: {value}\n")
                                    f.write("\n")
                                else:
                                    f.write(f"  {item}\n")
                        else:
                            f.write(f"  {data}\n")
                    else:
                        f.write("  No data found\n\n")
                
                # Write table details
                if self.schema_data.get('table_details'):
                    f.write("\nTABLE DETAILS WITH SAMPLE DATA:\n")
                    f.write("=" * 40 + "\n")
                    
                    for table_name, details in self.schema_data['table_details'].items():
                        f.write(f"\nTable: {table_name}\n")
                        f.write("-" * (len(table_name) + 7) + "\n")
                        
                        # Column information
                        f.write("Columns:\n")
                        for col_name, col_info in details.get('columns', {}).items():
                            f.write(f"  {col_name} ({col_info.get('data_type', 'unknown')})\n")
                            if col_info.get('statistics'):
                                stats = col_info['statistics']
                                f.write(f"    - Total rows: {stats.get('total_rows', 'N/A')}\n")
                                f.write(f"    - Null percentage: {stats.get('null_percentage', 'N/A')}%\n")
                                f.write(f"    - Distinct values: {stats.get('distinct_count', 'N/A')}\n")
                            
                            if col_info.get('distinct_values'):
                                f.write(f"    - Top values: {', '.join([str(v['value']) for v in col_info['distinct_values'][:5]])}\n")
                        
                        # Sample data
                        if details.get('sample_data'):
                            f.write("\nSample Data (first 5 rows):\n")
                            for i, row in enumerate(details['sample_data'][:5]):
                                f.write(f"  Row {i+1}: {row}\n")
                        f.write("\n")
                
                # Write AI insights if available
                if self.schema_data.get('ai_insights'):
                    f.write("\nAI INSIGHTS:\n")
                    f.write("=" * 20 + "\n")
                    ai_insights = self.schema_data['ai_insights']
                    
                    if ai_insights.get('table_classifications'):
                        f.write("\nTable Classifications:\n")
                        for table, classification in ai_insights['table_classifications'].items():
                            f.write(f"  {table}: {classification.get('type', 'unknown')} ({classification.get('confidence', 'low')} confidence)\n")
                
                # Write data quality analysis if available
                if self.schema_data.get('data_quality_analysis'):
                    f.write("\nDATA QUALITY ANALYSIS:\n")
                    f.write("=" * 30 + "\n")
                    quality = self.schema_data['data_quality_analysis']
                    
                    if quality.get('high_null_percentage_columns'):
                        f.write("\nHigh Null Percentage Columns:\n")
                        for issue in quality['high_null_percentage_columns']:
                            f.write(f"  {issue['table']}.{issue['column']}: {issue['null_percentage']}% nulls\n")
            
            logger.info(f"✓ Text export saved: {filepath}")
        except Exception as e:
            logger.error(f"✗ Failed to save text format: {e}")
            raise ExportWriterError(f"Failed to save text format: {e}")
    
    def save_exports(self, base_filename: str = None) -> List[str]:
        """Save exports in all configured formats to organized directories"""
        if not self.schema_data:
            raise ExportWriterError("No schema data to export. Set schema data first.")
        
        if base_filename is None:
            timestamp = self.export_timestamp.strftime("%Y%m%d_%H%M%S")
            database_name = self.config.get('database', 'unknown').replace('\\', '_').replace('/', '_')
            base_filename = f"{database_name}_schema_export_{timestamp}"
        
        saved_files = []
        
        # Create timestamped subdirectory for this export
        export_subdir = EXPORTS_DIR / f"{self.export_timestamp.strftime('%Y%m%d_%H%M%S')}"
        export_subdir.mkdir(exist_ok=True)
        
        logger.info(f"Saving exports to: {export_subdir}")
        
        # Save in all configured formats
        for format_type in self.config.get('output_formats', ['json']):
            filepath = export_subdir / f"{base_filename}.{format_type}"
            
            if format_type == 'json':
                self._save_json_format(filepath)
            elif format_type == 'xml':
                self._save_xml_format(filepath)
            elif format_type == 'txt':
                self._save_text_format(filepath)
            else:
                logger.warning(f"Unknown format type: {format_type}")
                continue
            
            saved_files.append(str(filepath))
            
            # Create backup on desktop if configured
            if self.config.get('backup_to_desktop', False):
                try:
                    desktop_path = Path.home() / "Desktop"
                    if desktop_path.exists():
                        backup_file = desktop_path / f"{base_filename}.{format_type}"
                        if format_type == 'json':
                            self._save_json_format(backup_file)
                        elif format_type == 'xml':
                            self._save_xml_format(backup_file)
                        elif format_type == 'txt':
                            self._save_text_format(backup_file)
                        logger.info(f"✓ Backup saved to desktop: {backup_file}")
                except Exception as e:
                    logger.warning(f"Could not create desktop backup: {e}")
        
        return saved_files
    
    def get_export_summary(self, saved_files: List[str]) -> Dict[str, Any]:
        """Get summary information about the export"""
        if not self.schema_data:
            return {}
        
        return {
            'export_timestamp': self.export_timestamp,
            'database': self.config.get('database'),
            'server': self.config.get('server'),
            'total_tables': len(self.schema_data.get('table_details', {})),
            'files_created': len(saved_files),
            'file_paths': saved_files,
            'export_directory': str(EXPORTS_DIR),
            'log_directory': str(LOGS_DIR),
            'backup_to_desktop': self.config.get('backup_to_desktop', False)
        }
