<#
.SYNOPSIS
    Removes Salient Security Platform software components, files, and registry entries.

.DESCRIPTION
    This script provides a comprehensive cleanup solution for Salient Security Platform installations.
    It removes remaining program files, application data, and registry entries after uninstallation.

    ⚠️  IMPORTANT: This script should ONLY be run AFTER CompleteView has been properly uninstalled
    through Windows Add/Remove Programs or the official uninstaller. This script cleans up any
    remaining files and registry entries that may be left behind after the standard uninstall process.

    Features:
    - Administrator privilege verification
    - Comprehensive error handling with verbose output
    - Automatic backup of registry keys and folders to desktop
    - Progress indicators and colored output
    - Detailed cleanup summary

.PARAMETER Force
    Skip confirmation prompts and proceed with cleanup automatically.

.EXAMPLE
    .\Remove-CV.ps1
    Run interactive cleanup after CompleteView has been uninstalled. Includes safety prompts and automatic backups.

.EXAMPLE
    .\Remove-CV.ps1 -Force
    Perform cleanup without prompts after CompleteView has been uninstalled. Still creates automatic backups.

.NOTES
    Author: PowerShell Script
    Version: 2.1
    Requires: PowerShell 5.0+, Administrator privileges

    Backups are automatically created on the user's desktop in a timestamped folder.

.LINK
    https://github.com/your-repo/salient-cleanup
#>

[CmdletBinding()]
param(
    [Parameter(HelpMessage = "Skip confirmation prompts")]
    [switch]$Force
)

# Requires Administrator privileges
#Requires -RunAsAdministrator

# Script configuration
$ErrorActionPreference = 'Continue'
$ProgressPreference = 'Continue'

# Initialize script variables
$script:BackupPath = Join-Path -Path ([Environment]::GetFolderPath('Desktop')) -ChildPath "SalientCleanup_Backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
$script:CleanupResults = @{
    FoldersDeleted = @()
    FoldersSkipped = @()
    FoldersFailed = @()
    FoldersBackedUp = @()
    RegistryDeleted = @()
    RegistrySkipped = @()
    RegistryFailed = @()
    RegistryBackedUp = @()
    StartTime = Get-Date
    EndTime = $null
}

# Define cleanup targets
$script:Config = @{
    Folders = @(
        "C:\Program Files\Salient Security Platform",
        "$env:ProgramData\Salient Security Platform",
        "$env:AppData\Salient Security Platform",
        "$env:LocalAppData\Salient Security Platform"
    )

    RegistryKeys = @(
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{F4424876-6DF0-4ECB-8AC2-58AF90996F35}",
        "HKLM:\SOFTWARE\Salient Security Platform",
        "HKLM:\SOFTWARE\WOW6432Node\Salient Security Platform",
        "HKCU:\SOFTWARE\Salient Security Platform"
    )
}

#region Helper Functions

function Write-Output {
    <#
    .SYNOPSIS
        Write message to console with appropriate formatting (always verbose).
    #>
    param(
        [Parameter(Mandatory)]
        [string]$Message,

        [ValidateSet('Info', 'Warning', 'Error', 'Success', 'Verbose')]
        [string]$Level = 'Info'
    )

    $timestamp = Get-Date -Format 'HH:mm:ss'
    $formattedMessage = "[$timestamp] $Message"

    # Write to console with colors (always verbose)
    switch ($Level) {
        'Info'    { Write-Host $formattedMessage -ForegroundColor White }
        'Warning' { Write-Host $formattedMessage -ForegroundColor Yellow }
        'Error'   { Write-Host $formattedMessage -ForegroundColor Red }
        'Success' { Write-Host $formattedMessage -ForegroundColor Green }
        'Verbose' { Write-Host $formattedMessage -ForegroundColor Cyan }
    }
}

function Initialize-BackupDirectory {
    <#
    .SYNOPSIS
        Create backup directory on user's desktop.
    #>
    try {
        if (-not (Test-Path -Path $script:BackupPath)) {
            New-Item -Path $script:BackupPath -ItemType Directory -Force | Out-Null
            Write-Output "Created backup directory: $script:BackupPath" -Level Success
        }
        return $true
    }
    catch {
        Write-Output "Failed to create backup directory: $_" -Level Error
        return $false
    }
}

function Test-AdministratorPrivileges {
    <#
    .SYNOPSIS
        Verify the script is running with administrator privileges.
    #>
    try {
        $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
        $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
        $isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

        if (-not $isAdmin) {
            Write-Output "Administrator privileges required. Please run as Administrator." -Level Error
            return $false
        }

        Write-Output "Administrator privileges verified." -Level Success
        return $true
    }
    catch {
        Write-Output "Failed to verify administrator privileges: $_" -Level Error
        return $false
    }
}

function Show-Banner {
    <#
    .SYNOPSIS
        Display script banner and information.
    #>
    $banner = @"

╔═════════════════════════════════════════════════════════════════╗
║                    CompleteView Cleanup Tool                    ║
║                                                                 ║
╚═════════════════════════════════════════════════════════════════╝

"@
    Write-Host $banner -ForegroundColor Cyan

    # Important warning message
    $warning = @"
⚠️  IMPORTANT NOTICE ⚠️
This script should ONLY be run AFTER CompleteView has been properly uninstalled
through Windows Add/Remove Programs or official uninstallers.

This cleanup tool removes any remaining files and registry entries left behind
after the standard uninstall process and should only be used in special circumstances.

Backups are automatically created on your desktop.
"@
    Write-Host $warning -ForegroundColor Yellow -BackgroundColor DarkRed

    Write-Output "Salient Security Platform Cleanup Tool v2.1 started" -Level Info
    Write-Output "Backup directory: $script:BackupPath" -Level Info
}

function Get-UserConfirmation {
    <#
    .SYNOPSIS
        Get user confirmation before proceeding with cleanup.
    #>
    param([string]$Message = "Do you want to proceed with the cleanup?")

    if ($Force) {
        Write-Output "Force mode enabled - skipping confirmation" -Level Verbose
        return $true
    }

    Write-Host "`n$Message" -ForegroundColor Yellow
    Write-Host "Type 'YES' to confirm or any other key to cancel: " -ForegroundColor Yellow -NoNewline

    $response = Read-Host
    $confirmed = $response -eq 'YES'

    Write-Output "User confirmation: $(if ($confirmed) { 'Confirmed' } else { 'Cancelled' })" -Level Info
    return $confirmed
}

#endregion

#region Registry Management

function Backup-RegistryKeys {
    <#
    .SYNOPSIS
        Create backup of registry keys before deletion to desktop.
    #>
    $registryBackupDir = Join-Path -Path $script:BackupPath -ChildPath "Registry"

    try {
        if (-not (Test-Path -Path $registryBackupDir)) {
            New-Item -Path $registryBackupDir -ItemType Directory -Force | Out-Null
        }

        Write-Output "Creating registry backup in: $registryBackupDir" -Level Info

        foreach ($regKey in $script:Config.RegistryKeys) {
            try {
                if (Test-Path $regKey) {
                    $keyName = ($regKey -split '\\')[-1] -replace '[^\w\-_]', '_'
                    $backupFile = Join-Path -Path $registryBackupDir -ChildPath "$keyName.reg"

                    # Convert PowerShell path to reg.exe format
                    $regPath = $regKey -replace '^HKLM:', 'HKEY_LOCAL_MACHINE' -replace '^HKCU:', 'HKEY_CURRENT_USER'
                    $regArgs = @('export', $regPath, $backupFile, '/y')

                    $process = Start-Process -FilePath 'reg.exe' -ArgumentList $regArgs -Wait -PassThru -WindowStyle Hidden
                    if ($process.ExitCode -eq 0) {
                        $script:CleanupResults.RegistryBackedUp += $regKey
                        Write-Output "Backed up registry key: $regKey" -Level Success
                    }
                    else {
                        Write-Output "Failed to backup registry key: $regKey" -Level Warning
                    }
                }
                else {
                    Write-Output "Registry key not found for backup: $regKey" -Level Verbose
                }
            }
            catch {
                Write-Output "Error backing up registry key $regKey`: $_" -Level Warning
            }
        }

        return $true
    }
    catch {
        Write-Output "Failed to create registry backup: $_" -Level Error
        return $false
    }
}

function Backup-SalientFolders {
    <#
    .SYNOPSIS
        Create backup of Salient folders before deletion to desktop.
    #>
    $foldersBackupDir = Join-Path -Path $script:BackupPath -ChildPath "Folders"

    try {
        if (-not (Test-Path -Path $foldersBackupDir)) {
            New-Item -Path $foldersBackupDir -ItemType Directory -Force | Out-Null
        }

        Write-Output "Creating folders backup in: $foldersBackupDir" -Level Info

        foreach ($folder in $script:Config.Folders) {
            $expandedPath = [Environment]::ExpandEnvironmentVariables($folder)

            if (Test-Path -Path $expandedPath) {
                try {
                    $folderName = Split-Path -Path $expandedPath -Leaf
                    $backupDestination = Join-Path -Path $foldersBackupDir -ChildPath $folderName

                    Write-Output "Backing up folder: $expandedPath" -Level Info
                    Copy-Item -Path $expandedPath -Destination $backupDestination -Recurse -Force -ErrorAction Stop

                    $script:CleanupResults.FoldersBackedUp += $expandedPath
                    Write-Output "Successfully backed up folder: $expandedPath" -Level Success
                }
                catch {
                    Write-Output "Failed to backup folder $expandedPath`: $_" -Level Warning
                }
            }
            else {
                Write-Output "Folder not found for backup: $expandedPath" -Level Verbose
            }
        }

        return $true
    }
    catch {
        Write-Output "Failed to create folders backup: $_" -Level Error
        return $false
    }
}

#endregion
#region Cleanup Functions

function Remove-SalientFolders {
    <#
    .SYNOPSIS
        Remove Salient Security Platform folders with comprehensive error handling.
    #>
    Write-Output "`n--- Cleaning Folders ---" -Level Info

    $totalFolders = $script:Config.Folders.Count
    $currentFolder = 0

    foreach ($folder in $script:Config.Folders) {
        $currentFolder++
        $expandedPath = [Environment]::ExpandEnvironmentVariables($folder)

        Write-Progress -Activity "Cleaning Folders" -Status "Processing: $expandedPath" -PercentComplete (($currentFolder / $totalFolders) * 100)
        Write-Output "Processing folder ($currentFolder/$totalFolders): $expandedPath" -Level Verbose

        if (Test-Path -Path $expandedPath) {
            try {
                # Get folder size information
                $folderSize = (Get-ChildItem -Path $expandedPath -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                $folderSizeMB = [math]::Round($folderSize / 1MB, 2)

                Write-Output "Found folder: $expandedPath (Size: $folderSizeMB MB)" -Level Info

                # Attempt to remove with retry logic
                $maxRetries = 3
                $retryCount = 0
                $removed = $false

                do {
                    try {
                        Remove-Item -Path $expandedPath -Recurse -Force -ErrorAction Stop
                        $removed = $true
                        $script:CleanupResults.FoldersDeleted += $expandedPath
                        Write-Output "Successfully deleted folder: $expandedPath" -Level Success
                    }
                    catch {
                        $retryCount++
                        if ($retryCount -lt $maxRetries) {
                            Write-Output "Retry $retryCount/$maxRetries for folder: $expandedPath" -Level Warning
                            Start-Sleep -Seconds 2
                        }
                        else {
                            throw $_
                        }
                    }
                } while (-not $removed -and $retryCount -lt $maxRetries)

                if (-not $removed) {
                    throw "Failed after $maxRetries attempts"
                }
            }
            catch {
                $script:CleanupResults.FoldersFailed += $expandedPath
                $errorDetails = $_.Exception.Message
                if ($_.Exception.InnerException) {
                    $errorDetails += " Inner: $($_.Exception.InnerException.Message)"
                }
                Write-Output "Failed to delete folder $expandedPath`: $errorDetails" -Level Error

                # Categorize error type
                if ($errorDetails -match "access.*denied|unauthorized") {
                    Write-Output "  → Permission denied. Ensure running as Administrator." -Level Error
                }
                elseif ($errorDetails -match "in use|being used") {
                    Write-Output "  → Files in use. Stop related processes and try again." -Level Error
                }
                elseif ($errorDetails -match "not found|does not exist") {
                    Write-Output "  → Path not found during deletion (may have been partially removed)." -Level Warning
                }
            }
        }
        else {
            $script:CleanupResults.FoldersSkipped += $expandedPath
            Write-Output "Folder does not exist: $expandedPath" -Level Verbose
        }
    }

    Write-Progress -Activity "Cleaning Folders" -Completed
}

function Remove-SalientRegistryKeys {
    <#
    .SYNOPSIS
        Remove Salient Security Platform registry keys with backup and error handling.
    #>
    Write-Output "`n--- Cleaning Registry Keys ---" -Level Info

    $totalKeys = $script:Config.RegistryKeys.Count
    $currentKey = 0

    foreach ($regKey in $script:Config.RegistryKeys) {
        $currentKey++
        Write-Progress -Activity "Cleaning Registry" -Status "Processing: $regKey" -PercentComplete (($currentKey / $totalKeys) * 100)
        Write-Output "Processing registry key ($currentKey/$totalKeys): $regKey" -Level Verbose

        if (Test-Path -Path $regKey) {
            try {
                # Get key information
                $keyInfo = Get-Item -Path $regKey -ErrorAction Stop
                $subKeyCount = $keyInfo.SubKeyCount
                $valueCount = $keyInfo.ValueCount

                Write-Output "Found registry key: $regKey (SubKeys: $subKeyCount, Values: $valueCount)" -Level Info

                Remove-Item -Path $regKey -Recurse -Force -ErrorAction Stop
                $script:CleanupResults.RegistryDeleted += $regKey
                Write-Output "Successfully deleted registry key: $regKey" -Level Success
            }
            catch {
                $script:CleanupResults.RegistryFailed += $regKey
                $errorDetails = $_.Exception.Message
                Write-Output "Failed to delete registry key $regKey`: $errorDetails" -Level Error

                # Categorize error type
                if ($errorDetails -match "access.*denied|unauthorized") {
                    Write-Output "  → Permission denied. Ensure running as Administrator." -Level Error
                }
                elseif ($errorDetails -match "in use|being used") {
                    Write-Output "  → Registry key in use. Stop related processes and try again." -Level Error
                }
            }
        }
        else {
            $script:CleanupResults.RegistrySkipped += $regKey
            Write-Output "Registry key does not exist: $regKey" -Level Verbose
        }
    }

    Write-Progress -Activity "Cleaning Registry" -Completed
}

#endregion
#region Reporting and Summary

function Show-CleanupSummary {
    <#
    .SYNOPSIS
        Display comprehensive cleanup summary with statistics and recommendations.
    #>
    $script:CleanupResults.EndTime = Get-Date
    $duration = $script:CleanupResults.EndTime - $script:CleanupResults.StartTime

    Write-Host "`n" -NoNewline
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                              CLEANUP SUMMARY                                ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan

    # Execution Summary
    Write-Host "`n📊 EXECUTION SUMMARY" -ForegroundColor Yellow
    Write-Host "   Duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor White
    Write-Host "   Mode: Live Cleanup" -ForegroundColor White
    Write-Host "   Backup Directory: $script:BackupPath" -ForegroundColor White



    # Folders Summary
    Write-Host "`n📁 FOLDERS" -ForegroundColor Yellow
    Write-Host "   Backed Up: $($script:CleanupResults.FoldersBackedUp.Count)" -ForegroundColor Cyan
    Write-Host "   Deleted: $($script:CleanupResults.FoldersDeleted.Count)" -ForegroundColor Green
    Write-Host "   Skipped: $($script:CleanupResults.FoldersSkipped.Count)" -ForegroundColor Gray
    Write-Host "   Failed: $($script:CleanupResults.FoldersFailed.Count)" -ForegroundColor Red

    if ($script:CleanupResults.FoldersBackedUp.Count -gt 0) {
        Write-Host "   Successfully Backed Up:" -ForegroundColor Cyan
        foreach ($folder in $script:CleanupResults.FoldersBackedUp) {
            Write-Host "     • $folder" -ForegroundColor Cyan
        }
    }

    if ($script:CleanupResults.FoldersDeleted.Count -gt 0) {
        Write-Host "   Successfully Deleted:" -ForegroundColor Green
        foreach ($folder in $script:CleanupResults.FoldersDeleted) {
            Write-Host "     • $folder" -ForegroundColor Green
        }
    }

    if ($script:CleanupResults.FoldersFailed.Count -gt 0) {
        Write-Host "   Failed to Delete:" -ForegroundColor Red
        foreach ($folder in $script:CleanupResults.FoldersFailed) {
            Write-Host "     • $folder" -ForegroundColor Red
        }
    }

    # Registry Summary
    Write-Host "`n🗂️  REGISTRY" -ForegroundColor Yellow
    Write-Host "   Backed Up: $($script:CleanupResults.RegistryBackedUp.Count)" -ForegroundColor Cyan
    Write-Host "   Deleted: $($script:CleanupResults.RegistryDeleted.Count)" -ForegroundColor Green
    Write-Host "   Skipped: $($script:CleanupResults.RegistrySkipped.Count)" -ForegroundColor Gray
    Write-Host "   Failed: $($script:CleanupResults.RegistryFailed.Count)" -ForegroundColor Red

    if ($script:CleanupResults.RegistryBackedUp.Count -gt 0) {
        Write-Host "   Successfully Backed Up:" -ForegroundColor Cyan
        foreach ($regKey in $script:CleanupResults.RegistryBackedUp) {
            Write-Host "     • $regKey" -ForegroundColor Cyan
        }
    }

    if ($script:CleanupResults.RegistryDeleted.Count -gt 0) {
        Write-Host "   Successfully Deleted:" -ForegroundColor Green
        foreach ($regKey in $script:CleanupResults.RegistryDeleted) {
            Write-Host "     • $regKey" -ForegroundColor Green
        }
    }

    if ($script:CleanupResults.RegistryFailed.Count -gt 0) {
        Write-Host "   Failed to Delete:" -ForegroundColor Red
        foreach ($regKey in $script:CleanupResults.RegistryFailed) {
            Write-Host "     • $regKey" -ForegroundColor Red
        }
    }

    # Overall Status
    $totalErrors = $script:CleanupResults.FoldersFailed.Count + $script:CleanupResults.RegistryFailed.Count
    $totalSuccess = $script:CleanupResults.FoldersDeleted.Count + $script:CleanupResults.RegistryDeleted.Count

    Write-Host "`n🎯 OVERALL STATUS" -ForegroundColor Yellow
    if ($totalErrors -eq 0) {
        Write-Host "   ✅ Cleanup completed successfully!" -ForegroundColor Green
    }
    elseif ($totalSuccess -gt 0 -and $totalErrors -gt 0) {
        Write-Host "   ⚠️  Cleanup completed with some errors" -ForegroundColor Yellow
        Write-Host "   Review the details above for specific issues" -ForegroundColor Yellow
    }
    else {
        Write-Host "   ❌ Cleanup failed" -ForegroundColor Red
        Write-Host "   Review the details above for specific issues" -ForegroundColor Red
    }

    # Recommendations
    if ($totalErrors -gt 0) {
        Write-Host "`n💡 RECOMMENDATIONS" -ForegroundColor Yellow

        if ($script:CleanupResults.FoldersFailed.Count -gt 0) {
            Write-Host "   • Close any applications using Salient files" -ForegroundColor Cyan
            Write-Host "   • Stop any Salient services manually" -ForegroundColor Cyan
            Write-Host "   • Restart computer and retry if files are locked" -ForegroundColor Cyan
        }

        if ($script:CleanupResults.RegistryFailed.Count -gt 0) {
            Write-Host "   • Ensure running as Administrator" -ForegroundColor Cyan
            Write-Host "   • Check if registry keys are protected" -ForegroundColor Cyan
        }
    }

    # Backup information
    if ($script:CleanupResults.FoldersBackedUp.Count -gt 0 -or $script:CleanupResults.RegistryBackedUp.Count -gt 0) {
        Write-Host "`n💾 BACKUP INFORMATION" -ForegroundColor Yellow
        Write-Host "   All backups are stored in: $script:BackupPath" -ForegroundColor Cyan
        Write-Host "   • Registry backups: $script:BackupPath\Registry" -ForegroundColor Cyan
        Write-Host "   • Folder backups: $script:BackupPath\Folders" -ForegroundColor Cyan
    }

    Write-Output "Cleanup summary completed" -Level Info
}

function Test-CleanupSuccess {
    <#
    .SYNOPSIS
        Verify cleanup was successful by checking if targets still exist.
    #>
    Write-Output "`n--- Verifying Cleanup Success ---" -Level Info

    $remainingItems = @()

    # Check folders
    foreach ($folder in $script:Config.Folders) {
        $expandedPath = [Environment]::ExpandEnvironmentVariables($folder)
        if (Test-Path -Path $expandedPath) {
            $remainingItems += "Folder: $expandedPath"
        }
    }

    # Check registry keys
    foreach ($regKey in $script:Config.RegistryKeys) {
        if (Test-Path -Path $regKey) {
            $remainingItems += "Registry: $regKey"
        }
    }

    if ($remainingItems.Count -eq 0) {
        Write-Output "✅ Verification successful - all targets removed" -Level Success
        return $true
    }
    else {
        Write-Output "⚠️  Verification found remaining items:" -Level Warning
        foreach ($item in $remainingItems) {
            Write-Output "   • $item" -Level Warning
        }
        return $false
    }
}

#endregion
#region Main Execution

function Start-SalientCleanup {
    <#
    .SYNOPSIS
        Main cleanup orchestration function.
    #>
    try {
        # Initialize backup directory
        if (-not (Initialize-BackupDirectory)) {
            Write-Output "Failed to initialize backup directory, continuing anyway..." -Level Warning
        }

        # Show banner
        Show-Banner

        # Verify administrator privileges
        if (-not (Test-AdministratorPrivileges)) {
            Write-Output "Exiting due to insufficient privileges" -Level Error
            return $false
        }

        # Confirm prerequisite - CompleteView should be uninstalled first
        $prerequisiteMessage = "⚠️  PREREQUISITE CHECK ⚠️`n`nHave you already uninstalled CompleteView through Windows Add/Remove Programs or the official uninstaller?`n`nThis cleanup script should ONLY be run AFTER the standard uninstall process."

        if (-not (Get-UserConfirmation -Message $prerequisiteMessage)) {
            Write-Output "Please uninstall CompleteView first through the standard uninstall process, then run this cleanup script." -Level Warning
            return $false
        }

        # Get user confirmation for cleanup
        $confirmMessage = "⚠️  WARNING: This will permanently delete remaining Salient Security Platform files and registry entries.`nBackups will be created on your desktop.`nDo you want to proceed with the cleanup?"

        if (-not (Get-UserConfirmation -Message $confirmMessage)) {
            Write-Output "Cleanup cancelled by user" -Level Info
            return $false
        }

        # Create backups
        Write-Output "Starting Salient Security Platform cleanup process..." -Level Info
        Write-Output "Creating backups before cleanup..." -Level Info

        $folderBackupResult = Backup-SalientFolders
        if (-not $folderBackupResult) {
            Write-Output "Folder backup failed. Continue without backup?" -Level Warning
            if (-not (Get-UserConfirmation -Message "Continue cleanup without folder backup?")) {
                Write-Output "Cleanup cancelled due to backup failure" -Level Info
                return $false
            }
        }

        $registryBackupResult = Backup-RegistryKeys
        if (-not $registryBackupResult) {
            Write-Output "Registry backup failed. Continue without backup?" -Level Warning
            if (-not (Get-UserConfirmation -Message "Continue cleanup without registry backup?")) {
                Write-Output "Cleanup cancelled due to backup failure" -Level Info
                return $false
            }
        }

        # Perform cleanup
        Remove-SalientFolders
        Remove-SalientRegistryKeys

        # Verify cleanup
        Test-CleanupSuccess | Out-Null

        # Show summary
        Show-CleanupSummary

        Write-Output "Cleanup process completed" -Level Info
        return $true
    }
    catch {
        Write-Output "Critical error during cleanup: $_" -Level Error
        Write-Output "Stack trace: $($_.ScriptStackTrace)" -Level Error
        return $false
    }
}

#endregion

#region Script Entry Point

# Main script execution
try {
    # Execute cleanup
    $result = Start-SalientCleanup

    # Set exit code based on result
    if ($result) {
        Write-Host "`n✅ Script completed successfully" -ForegroundColor Green
        Write-Host "💡 Consider restarting your computer to complete the cleanup" -ForegroundColor Cyan
        Write-Host "💾 Backups are stored in: $script:BackupPath" -ForegroundColor Cyan
        exit 0
    }
    else {
        Write-Host "`n❌ Script completed with errors" -ForegroundColor Red
        Write-Host "📋 Review the details above for specific issues" -ForegroundColor Yellow
        exit 1
    }
}
catch {
    Write-Host "`n💥 Critical script error: $_" -ForegroundColor Red
    Write-Host "📋 Review the error details above" -ForegroundColor Yellow
    exit 2
}
finally {
    # Clean up and final message
    if (-not $Force) {
        Write-Host "`nPress any key to exit..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
}

#endregion
