#!/usr/bin/env python3
"""
Main schema exporter orchestrator for the Comprehensive Schema Export Tool
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from .config_manager import ConfigManager
from .database_connector import DatabaseConnector
from .query_generator import QueryGenerator
from .ai_analyzer import AIAnalyzer
from .data_quality_analyzer import DataQualityAnalyzer
from .export_writer import ExportWriter
from .exceptions import SchemaExportError
from .utils import setup_logging, format_duration, create_export_summary, print_progress_bar

logger = logging.getLogger(__name__)

# Directory constants
SCRIPT_DIR = Path(__file__).parent.absolute()
EXPORTS_DIR = SCRIPT_DIR / "exports"
LOGS_DIR = SCRIPT_DIR / "logs"


class SchemaExporter:
    """Main orchestrator for comprehensive schema export"""
    
    def __init__(self, config: Dict = None):
        """Initialize schema exporter with all components"""
        # Setup logging first
        setup_logging(LOGS_DIR)
        
        # Initialize components
        self.config_manager = ConfigManager(config)
        self.db_connector = DatabaseConnector(self.config_manager)
        self.query_generator = QueryGenerator(self.config_manager)
        self.ai_analyzer = AIAnalyzer(self.config_manager, self.db_connector)
        self.data_quality_analyzer = DataQualityAnalyzer(self.config_manager, self.db_connector)
        self.export_writer = ExportWriter(self.config_manager)
        
        # Export state
        self.schema_data = None
        self.export_timestamp = None
        
        logger.info("Schema exporter initialized successfully")
    
    @classmethod
    def from_config_file(cls, config_path: str) -> 'SchemaExporter':
        """Create schema exporter from configuration file"""
        config_manager = ConfigManager.from_file(config_path)
        return cls(config_manager.to_dict())
    
    def export_schema(self) -> Dict[str, Any]:
        """Main method to export comprehensive schema information"""
        try:
            start_time = datetime.now()
            self.export_timestamp = start_time
            
            logger.info("=" * 60)
            logger.info("STARTING COMPREHENSIVE SCHEMA EXPORT")
            logger.info("=" * 60)
            logger.info(f"Database: {self.config_manager.get('database')}")
            logger.info(f"Server: {self.config_manager.get('server')}")
            logger.info(f"Export timestamp: {start_time}")
            
            with self.db_connector.get_connection() as conn:
                cursor = conn.cursor()
                
                # Step 1: Execute comprehensive queries
                logger.info("Step 1: Executing comprehensive schema queries...")
                schema_info = self._execute_comprehensive_queries(cursor)
                
                # Step 2: Get table list and process table details
                logger.info("Step 2: Processing table details with sampling...")
                table_details = self._process_table_details(cursor)
                
                # Step 3: Generate AI insights
                logger.info("Step 3: Generating AI-specific insights...")
                ai_insights = self._generate_ai_insights(cursor, table_details)
                
                # Step 4: Analyze data quality
                logger.info("Step 4: Analyzing data quality...")
                data_quality_analysis = self._analyze_data_quality(cursor, table_details)
                
                # Step 5: Compile final schema data
                logger.info("Step 5: Compiling final schema data...")
                self.schema_data = self._compile_schema_data(
                    schema_info, table_details, ai_insights, data_quality_analysis
                )
            
            # Calculate execution time
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info("=" * 60)
            logger.info(f"SCHEMA EXPORT COMPLETED in {format_duration(duration.total_seconds())}")
            logger.info("=" * 60)
            
            return self.schema_data
            
        except Exception as e:
            logger.error(f"Schema export failed: {e}")
            raise SchemaExportError(f"Schema export failed: {e}")
    
    def save_exports(self, base_filename: str = None) -> List[str]:
        """Save exports in all configured formats"""
        if not self.schema_data:
            raise SchemaExportError("No schema data to export. Run export_schema() first.")
        
        # Set schema data in export writer
        self.export_writer.set_schema_data(self.schema_data, self.export_timestamp)
        
        # Save exports
        saved_files = self.export_writer.save_exports(base_filename)
        
        # Get export summary
        summary = self.export_writer.get_export_summary(saved_files)
        summary['script_root'] = str(SCRIPT_DIR)
        summary['log_file'] = str(LOGS_DIR / f"schema_export_{datetime.now().strftime('%Y%m%d')}.log")
        
        # Print summary
        print(create_export_summary(summary))
        
        return saved_files
    
    def _execute_comprehensive_queries(self, cursor) -> Dict[str, List]:
        """Execute all comprehensive schema queries"""
        queries = self.query_generator.get_comprehensive_queries()
        schema_info = {}
        
        total_queries = len(queries)
        for i, (query_name, query_sql) in enumerate(queries.items(), 1):
            print_progress_bar(i, total_queries, f"Executing queries", f"({i}/{total_queries})")
            
            try:
                results = self.db_connector.execute_query_safely(cursor, query_sql, query_name)
                
                # Convert results to list of dictionaries
                if results:
                    columns = [desc[0] for desc in cursor.description]
                    schema_info[query_name] = [dict(zip(columns, row)) for row in results]
                else:
                    schema_info[query_name] = []
                    
            except Exception as e:
                logger.warning(f"Query '{query_name}' failed: {e}")
                schema_info[query_name] = []
        
        return schema_info
    
    def _process_table_details(self, cursor) -> Dict[str, Any]:
        """Process table details with sampling and statistics"""
        # Get table list
        table_query = self.query_generator.get_table_list_query()
        table_info = self.db_connector.execute_query_safely(cursor, table_query, "Table list with row counts")
        
        # Apply table count limit and log information about restrictions
        max_tables = self.config_manager.get('max_tables_to_sample')
        total_tables_found = len(table_info)
        
        # Log table size distribution
        if table_info:
            sizes = [row[1] for row in table_info]
            logger.info(f"Found {total_tables_found} tables for processing")
            logger.info(f"Table size range: {min(sizes):,} to {max(sizes):,} rows")
            
            # Count tables by size categories
            small_tables = sum(1 for size in sizes if size <= 10000)
            medium_tables = sum(1 for size in sizes if 10000 < size <= 100000)
            large_tables = sum(1 for size in sizes if 100000 < size <= 1000000)
            very_large_tables = sum(1 for size in sizes if size > 1000000)
            
            logger.info(f"Table size distribution: {small_tables} small (≤10K), {medium_tables} medium (10K-100K), "
                       f"{large_tables} large (100K-1M), {very_large_tables} very large (>1M)")
        
        if len(table_info) > max_tables:
            logger.info(f"Limiting to {max_tables} tables for detailed sampling (performance protection)")
            table_info = table_info[:max_tables]
        
        # Process each table
        table_details = {}
        for i, (table_name, estimated_rows) in enumerate(table_info, 1):
            print_progress_bar(i, len(table_info), f"Processing tables", f"({i}/{len(table_info)}) {table_name}")
            
            table_details[table_name] = {
                'estimated_rows': estimated_rows,
                'columns': {},
                'sample_data': []
            }
            
            # Check if table is too large for various operations
            max_sampling_rows = self.config_manager.get('max_table_rows_for_sampling')
            max_stats_rows = self.config_manager.get('max_table_rows_for_stats')
            
            skip_sampling = estimated_rows > max_sampling_rows
            skip_stats = estimated_rows > max_stats_rows
            
            # Log what operations will be skipped for large tables
            if skip_sampling or skip_stats:
                skipped_ops = []
                if skip_sampling:
                    skipped_ops.append(f"data sampling (>{max_sampling_rows:,} rows)")
                if skip_stats:
                    skipped_ops.append(f"column statistics (>{max_stats_rows:,} rows)")
                logger.info(f"Table {table_name}: Skipping {', '.join(skipped_ops)} due to size ({estimated_rows:,} rows)")
            
            # Get sample data (only if table is not too large)
            if not skip_sampling:
                sample_data = self.ai_analyzer.get_table_sample_data(cursor, table_name, self.config_manager.get('sample_size'))
                table_details[table_name]['sample_data'] = sample_data
            
            # Get column information
            col_query = self.query_generator.get_column_info_query(table_name)
            col_results = self.db_connector.execute_query_safely(cursor, col_query, f"Column info for {table_name}")
            
            # Get statistics for each column
            for col_name, data_type in col_results:
                # Get column statistics (only if not too large)
                stats = {}
                if not skip_stats:
                    stats = self.ai_analyzer.get_column_statistics(cursor, table_name, col_name)
                
                # Get distinct values for categorical columns
                distinct_values = []
                if (not skip_stats and data_type in ['varchar', 'nvarchar', 'char', 'nchar', 'text'] 
                    and stats.get('distinct_count', 0) <= self.config_manager.get('max_distinct_values')):
                    distinct_values = self.ai_analyzer.get_distinct_values(
                        cursor, table_name, col_name, self.config_manager.get('max_distinct_values')
                    )
                
                # Analyze column patterns for AI insights (only if not too large)
                patterns = {}
                if not skip_stats:
                    patterns = self.ai_analyzer.analyze_column_patterns(cursor, table_name, col_name, data_type)
                
                table_details[table_name]['columns'][col_name] = {
                    'data_type': data_type,
                    'statistics': stats,
                    'distinct_values': distinct_values,
                    'patterns': patterns
                }
        
        return table_details
    
    def _generate_ai_insights(self, cursor, table_details: Dict) -> Dict[str, Any]:
        """Generate AI-specific insights and metadata"""
        ai_insights = {}
        
        # Generate AI hints
        ai_hints = self.ai_analyzer.generate_ai_hints(table_details)
        ai_insights.update(ai_hints)
        
        # Detect table relationships
        detected_relationships = self.ai_analyzer.detect_table_relationships(cursor)
        ai_insights['detected_relationships'] = detected_relationships
        
        return ai_insights
    
    def _analyze_data_quality(self, cursor, table_details: Dict) -> Dict[str, Any]:
        """Analyze data quality issues"""
        return self.data_quality_analyzer.analyze_data_quality(cursor, table_details)
    
    def _compile_schema_data(self, schema_info: Dict, table_details: Dict, 
                           ai_insights: Dict, data_quality_analysis: Dict) -> Dict[str, Any]:
        """Compile final schema data structure"""
        return {
            'export_metadata': {
                'export_timestamp': self.export_timestamp.isoformat(),
                'database_server': self.config_manager.get('server'),
                'database_name': self.config_manager.get('database'),
                'export_version': '3.0',
                'total_tables_found': len(table_details),
                'tables_sampled': len(table_details),
                'max_table_rows_for_sampling': self.config_manager.get('max_table_rows_for_sampling'),
                'max_table_rows_for_stats': self.config_manager.get('max_table_rows_for_stats'),
                'schema_filter_mode': self.config_manager.get('schema_filter_mode'),
                'ai_features_enabled': {
                    'system_stats': self.config_manager.get('include_system_stats', True),
                    'column_patterns': self.config_manager.get('analyze_column_patterns', True),
                    'relationship_detection': self.config_manager.get('detect_table_relationships', True),
                    'ai_hints': self.config_manager.get('generate_ai_hints', True),
                    'query_examples': self.config_manager.get('export_query_examples', True),
                    'data_quality_analysis': self.config_manager.get('analyze_column_patterns', True)
                }
            },
            'schema_information': schema_info,
            'table_details': table_details,
            'ai_insights': ai_insights,
            'data_quality_analysis': data_quality_analysis
        }
