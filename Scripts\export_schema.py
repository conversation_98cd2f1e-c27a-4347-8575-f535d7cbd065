import pyodbc

conn_str = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=REMD-ZS-JV6GJV2\SQLEXPRESS;"
    "DATABASE=CompleteViewVms;"
    "UID=sa;"
    "PWD=Zipper091584!"
)

queries = {
    "Tables and Columns": """
        SELECT 
            TABLE_SCHEMA + '.' + TABLE_NAME AS FullTableName,
            COLUMN_NAME,
            DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        ORDER BY FullTableName, ORDINAL_POSITION;
    """,
    "Primary Keys": """
        SELECT 
            KU.TABLE_SCHEMA + '.' + KU.TABLE_NAME AS FullTableName,
            KU.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS TC
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE KU 
            ON TC.CONSTRAINT_NAME = KU.CONSTRAINT_NAME
        WHERE TC.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ORDER BY FullTableName;
    """,
    "Foreign Keys": """
        SELECT 
            FK.TABLE_SCHEMA + '.' + FK.TABLE_NAME AS FK_FullTable,
            CU.COLUMN_NAME AS FK_Column,
            PK.TABLE_SCHEMA + '.' + PK.TABLE_NAME AS PK_FullTable,
            PT.COLUMN_NAME AS PK_Column
        FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS C
        JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS FK 
            ON C.CONSTRAINT_NAME = FK.CONSTRAINT_NAME
        JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS PK 
            ON C.UNIQUE_CONSTRAINT_NAME = PK.CONSTRAINT_NAME
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE CU 
            ON C.CONSTRAINT_NAME = CU.CONSTRAINT_NAME
        JOIN (
            SELECT 
                i1.TABLE_SCHEMA + '.' + i1.TABLE_NAME AS FullTableName,
                i2.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS i1
            JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE i2 
                ON i1.CONSTRAINT_NAME = i2.CONSTRAINT_NAME
            WHERE i1.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ) PT ON PT.FullTableName = PK.TABLE_SCHEMA + '.' + PK.TABLE_NAME
        ORDER BY FK_FullTable;
    """,
    "Indexes": """
        SELECT 
            s.name + '.' + t.name AS FullTableName,
            ind.name AS IndexName,
            col.name AS ColumnName
        FROM sys.indexes ind 
        INNER JOIN sys.index_columns ic ON ind.object_id = ic.object_id AND ind.index_id = ic.index_id
        INNER JOIN sys.columns col ON ic.object_id = col.object_id AND ic.column_id = col.column_id
        INNER JOIN sys.tables t ON ind.object_id = t.object_id
        INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
        WHERE ind.is_primary_key = 0 AND ind.is_unique_constraint = 0;
    """,
    "Row Counts": """
        SELECT 
            s.name + '.' + t.name AS FullTableName,
            SUM(p.rows) AS RowCounts
        FROM 
            sys.tables t
        INNER JOIN 
            sys.partitions p ON t.object_id = p.object_id
        INNER JOIN 
            sys.schemas s ON t.schema_id = s.schema_id
        WHERE 
            p.index_id IN (0,1)
        GROUP BY 
            s.name, t.name
        ORDER BY 
            RowCounts DESC;
    """,
    "Unique Constraints": """
        SELECT 
            s.name + '.' + t.name AS FullTableName,
            i.name AS ConstraintName,
            col.name AS ColumnName
        FROM sys.indexes i
        JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
        JOIN sys.columns col ON ic.object_id = col.object_id AND ic.column_id = col.column_id
        JOIN sys.tables t ON i.object_id = t.object_id
        JOIN sys.schemas s ON t.schema_id = s.schema_id
        WHERE i.is_unique = 1 AND i.is_primary_key = 0;
    """,
    "Nullability and Defaults": """
        SELECT 
            TABLE_SCHEMA + '.' + TABLE_NAME AS FullTableName,
            COLUMN_NAME, 
            IS_NULLABLE, 
            COLUMN_DEFAULT 
        FROM INFORMATION_SCHEMA.COLUMNS
        ORDER BY FullTableName, ORDINAL_POSITION;
    """,
    "Triggers": """
        SELECT 
            s.name + '.' + t.name AS FullTableName,
            tr.name AS TriggerName,
            tr.is_disabled
        FROM sys.triggers tr
        JOIN sys.tables t ON tr.parent_id = t.object_id
        JOIN sys.schemas s ON t.schema_id = s.schema_id;
    """,
    "Trigger Definitions": """
        SELECT 
            s.name + '.' + t.name AS FullTableName,
            tr.name AS TriggerName,
            m.definition AS SqlCode
        FROM sys.triggers tr
        JOIN sys.tables t ON tr.parent_id = t.object_id
        JOIN sys.schemas s ON t.schema_id = s.schema_id
        JOIN sys.sql_modules m ON tr.object_id = m.object_id;
    """,
    "Views": """
        SELECT 
            TABLE_SCHEMA + '.' + TABLE_NAME AS FullViewName
        FROM INFORMATION_SCHEMA.VIEWS
        ORDER BY FullViewName;
    """,
    "View Definitions": """
        SELECT 
            TABLE_SCHEMA + '.' + TABLE_NAME AS FullViewName,
            VIEW_DEFINITION
        FROM INFORMATION_SCHEMA.VIEWS
        ORDER BY FullViewName;
    """,
    "Stored Procedures": """
        SELECT 
            ROUTINE_SCHEMA + '.' + SPECIFIC_NAME AS FullProcName,
            ROUTINE_TYPE
        FROM INFORMATION_SCHEMA.ROUTINES
        ORDER BY ROUTINE_TYPE, FullProcName;
    """,
    "Stored Procedure Parameters": """
        SELECT 
            SPECIFIC_SCHEMA + '.' + SPECIFIC_NAME AS FullProcName,
            PARAMETER_NAME, 
            DATA_TYPE
        FROM INFORMATION_SCHEMA.PARAMETERS
        ORDER BY FullProcName, ORDINAL_POSITION;
    """,
    "Stored Procedure Definitions": """
        SELECT 
            s.name + '.' + o.name AS FullObjectName,
            o.type_desc AS ObjectType,
            m.definition AS SqlCode
        FROM sys.sql_modules m
        JOIN sys.objects o ON m.object_id = o.object_id
        JOIN sys.schemas s ON o.schema_id = s.schema_id
        WHERE o.type IN ('P', 'FN', 'TF', 'IF')
        ORDER BY o.type, FullObjectName;
    """,
    "Check Constraints": """
        SELECT 
            s.name + '.' + t.name AS FullTableName,
            c.name AS ConstraintName,
            cc.definition AS CheckDefinition
        FROM sys.check_constraints cc
        JOIN sys.tables t ON cc.parent_object_id = t.object_id
        JOIN sys.objects c ON cc.object_id = c.object_id
        JOIN sys.schemas s ON t.schema_id = s.schema_id
        ORDER BY FullTableName;
    """,
    "Default Constraints": """
        SELECT 
            s.name + '.' + t.name AS FullTableName,
            c.name AS ColumnName,
            dc.name AS ConstraintName,
            dc.definition AS DefaultValue
        FROM sys.default_constraints dc
        JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
        JOIN sys.tables t ON t.object_id = c.object_id
        JOIN sys.schemas s ON t.schema_id = s.schema_id
        ORDER BY FullTableName, c.name;
    """,
    "Extended Properties": """
        SELECT 
            ep.name AS PropertyName,
            ep.value AS PropertyValue,
            s.name + '.' + obj.name AS FullObjectName,
            obj.type_desc AS ObjectType
        FROM sys.extended_properties ep
        JOIN sys.objects obj ON ep.major_id = obj.object_id
        JOIN sys.schemas s ON obj.schema_id = s.schema_id
        ORDER BY FullObjectName;
    """,
    "Object Dependencies": """
        SELECT 
            s1.name + '.' + OBJECT_NAME(d.referencing_id) AS ReferencingObject,
            s2.name + '.' + OBJECT_NAME(d.referenced_id) AS ReferencedObject
        FROM sys.sql_expression_dependencies d
        JOIN sys.objects o1 ON d.referencing_id = o1.object_id
        JOIN sys.objects o2 ON d.referenced_id = o2.object_id
        JOIN sys.schemas s1 ON o1.schema_id = s1.schema_id
        JOIN sys.schemas s2 ON o2.schema_id = s2.schema_id
        WHERE d.referenced_id IS NOT NULL
        ORDER BY ReferencingObject;
    """
}

output_file = "sql_server_schema_export.txt"

with open(output_file, "w", encoding="utf-8") as f:
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        for label, sql in queries.items():
            f.write(f"\n--- {label} ---\n")
            cursor.execute(sql)
            columns = [desc[0] for desc in cursor.description]
            for row in cursor.fetchall():
                f.write(", ".join(f"{col}: {val}" for col, val in zip(columns, row)) + "\n")

        print(f"Export complete: {output_file}")
    except Exception as e:
        print("Error:", e)
