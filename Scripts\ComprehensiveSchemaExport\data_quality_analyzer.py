#!/usr/bin/env python3
"""
Data quality analysis for the Comprehensive Schema Export Tool
"""

import logging
from typing import Dict, List, Any
from .config_manager import ConfigManager
from .database_connector import DatabaseConnector
from .exceptions import AIAnalysisError

logger = logging.getLogger(__name__)


class DataQualityAnalyzer:
    """Analyzes data quality issues for AI insights"""
    
    def __init__(self, config_manager: ConfigManager, db_connector: DatabaseConnector):
        """Initialize data quality analyzer"""
        self.config = config_manager
        self.db_connector = db_connector
    
    def analyze_data_quality(self, cursor, table_details: Dict) -> Dict:
        """Analyze data quality issues for AI insights"""
        if not self.config.get('analyze_column_patterns', True):
            return {}
        
        try:
            logger.info("Analyzing data quality issues...")
            
            quality_issues = {
                'high_null_percentage_columns': [],
                'potential_orphaned_records': [],
                'date_range_anomalies': [],
                'duplicate_value_concerns': [],
                'data_type_inconsistencies': [],
                'referential_integrity_issues': []
            }
            
            # Analyze column-level quality issues
            for table_name, details in table_details.items():
                for col_name, col_info in details.get('columns', {}).items():
                    stats = col_info.get('statistics', {})
                    data_type = col_info.get('data_type', '').lower()
                    
                    # Flag columns with high null percentage
                    null_pct = stats.get('null_percentage', 0)
                    if null_pct > 50:  # More than 50% nulls
                        quality_issues['high_null_percentage_columns'].append({
                            'table': table_name,
                            'column': col_name,
                            'null_percentage': null_pct,
                            'total_rows': stats.get('total_rows', 0),
                            'severity': 'high' if null_pct > 80 else 'medium',
                            'ai_impact': 'High null percentage may affect data analysis and ML model training'
                        })
                    
                    # Flag potential duplicate concerns (low uniqueness in non-key columns)
                    uniqueness_ratio = stats.get('uniqueness_ratio', 100)
                    distinct_count = stats.get('distinct_count', 0)
                    total_rows = stats.get('total_rows', 0)
                    
                    if (total_rows > 100 and uniqueness_ratio < 10 and 
                        not any(word in col_name.lower() for word in ['status', 'type', 'category', 'flag', 'code'])):
                        quality_issues['duplicate_value_concerns'].append({
                            'table': table_name,
                            'column': col_name,
                            'uniqueness_ratio': uniqueness_ratio,
                            'distinct_count': distinct_count,
                            'total_rows': total_rows,
                            'ai_impact': 'Low uniqueness may indicate data quality issues or missing normalization'
                        })
                    
                    # Analyze date range anomalies
                    if data_type in ['datetime', 'datetime2', 'date', 'smalldatetime']:
                        date_anomalies = self._check_date_anomalies(cursor, table_name, col_name)
                        if date_anomalies:
                            quality_issues['date_range_anomalies'].extend(date_anomalies)
            
            # Check for potential orphaned records
            orphaned_records = self._check_orphaned_records(cursor, table_details)
            quality_issues['potential_orphaned_records'].extend(orphaned_records)
            
            # Check referential integrity issues
            ref_integrity_issues = self._check_referential_integrity(cursor)
            quality_issues['referential_integrity_issues'].extend(ref_integrity_issues)
            
            return quality_issues
            
        except Exception as e:
            logger.warning(f"Could not complete data quality analysis: {e}")
            return {}
    
    def _check_date_anomalies(self, cursor, table_name: str, column_name: str) -> List[Dict]:
        """Check for date range anomalies in date columns"""
        try:
            anomaly_query = f"""
                SELECT 
                    'future_dates' AS AnomalyType,
                    COUNT(*) AS AnomalyCount,
                    MIN([{column_name}]) AS MinValue,
                    MAX([{column_name}]) AS MaxValue
                FROM [{table_name}]
                WHERE [{column_name}] > DATEADD(YEAR, 1, GETDATE())
                AND [{column_name}] IS NOT NULL
                HAVING COUNT(*) > 0
                
                UNION ALL
                
                SELECT 
                    'historical_dates' AS AnomalyType,
                    COUNT(*) AS AnomalyCount,
                    MIN([{column_name}]) AS MinValue,
                    MAX([{column_name}]) AS MaxValue
                FROM [{table_name}]
                WHERE [{column_name}] < '1900-01-01'
                AND [{column_name}] IS NOT NULL
                HAVING COUNT(*) > 0
                
                UNION ALL
                
                SELECT 
                    'default_dates' AS AnomalyType,
                    COUNT(*) AS AnomalyCount,
                    MIN([{column_name}]) AS MinValue,
                    MAX([{column_name}]) AS MaxValue
                FROM [{table_name}]
                WHERE [{column_name}] IN ('1900-01-01', '1753-01-01', '1970-01-01')
                HAVING COUNT(*) > 0
            """
            
            results = self.db_connector.execute_query_safely(cursor, anomaly_query, f"Date anomalies for {table_name}.{column_name}")
            
            anomalies = []
            for row in results:
                anomaly_type = row[0]
                count = row[1]
                min_val = row[2]
                max_val = row[3]
                
                severity = 'high' if count > 100 else 'medium' if count > 10 else 'low'
                
                anomalies.append({
                    'table': table_name,
                    'column': column_name,
                    'anomaly_type': anomaly_type,
                    'anomaly_count': count,
                    'min_value': str(min_val),
                    'max_value': str(max_val),
                    'severity': severity,
                    'ai_impact': f'{anomaly_type.replace("_", " ").title()} may indicate data entry errors or system defaults'
                })
            
            return anomalies
            
        except Exception as e:
            logger.warning(f"Could not check date anomalies for {table_name}.{column_name}: {e}")
            return []
    
    def _check_orphaned_records(self, cursor, table_details: Dict) -> List[Dict]:
        """Check for potential orphaned records (foreign key violations)"""
        try:
            schema_filter = self.config.get_schema_filter_clause()
            
            # Get foreign key relationships
            fk_query = f"""
                SELECT 
                    SCHEMA_NAME(tp.schema_id) + '.' + tp.name AS ChildTable,
                    cp.name AS ChildColumn,
                    SCHEMA_NAME(tr.schema_id) + '.' + tr.name AS ParentTable,
                    cr.name AS ParentColumn
                FROM sys.foreign_keys fk
                JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
                JOIN sys.tables tp ON fkc.parent_object_id = tp.object_id
                JOIN sys.tables tr ON fkc.referenced_object_id = tr.object_id
                JOIN sys.columns cp ON fkc.parent_object_id = cp.object_id AND fkc.parent_column_id = cp.column_id
                JOIN sys.columns cr ON fkc.referenced_object_id = cr.object_id AND fkc.referenced_column_id = cr.column_id
                WHERE 1=1 {schema_filter.replace('SCHEMA_NAME(t.schema_id)', 'SCHEMA_NAME(tp.schema_id)')}
            """
            
            fk_results = self.db_connector.execute_query_safely(cursor, fk_query, "Foreign key relationships for orphan check")
            
            orphaned_records = []
            for row in fk_results[:5]:  # Limit to first 5 FK relationships for performance
                child_table = row[0]
                child_column = row[1]
                parent_table = row[2]
                parent_column = row[3]
                
                # Check for orphaned records
                orphan_query = f"""
                    SELECT COUNT(*) AS OrphanCount
                    FROM [{child_table}] c
                    LEFT JOIN [{parent_table}] p ON c.[{child_column}] = p.[{parent_column}]
                    WHERE c.[{child_column}] IS NOT NULL 
                    AND p.[{parent_column}] IS NULL
                """
                
                orphan_results = self.db_connector.execute_query_safely(cursor, orphan_query, f"Orphan check for {child_table}")
                
                if orphan_results and orphan_results[0][0] > 0:
                    orphan_count = orphan_results[0][0]
                    severity = 'high' if orphan_count > 100 else 'medium' if orphan_count > 10 else 'low'
                    
                    orphaned_records.append({
                        'child_table': child_table,
                        'child_column': child_column,
                        'parent_table': parent_table,
                        'parent_column': parent_column,
                        'orphan_count': orphan_count,
                        'severity': severity,
                        'ai_impact': 'Orphaned records may indicate data integrity issues and affect JOIN operations'
                    })
            
            return orphaned_records
            
        except Exception as e:
            logger.warning(f"Could not check for orphaned records: {e}")
            return []
    
    def _check_referential_integrity(self, cursor) -> List[Dict]:
        """Check for referential integrity issues"""
        try:
            # Check for disabled foreign keys
            disabled_fk_query = """
                SELECT 
                    SCHEMA_NAME(tp.schema_id) + '.' + tp.name AS TableName,
                    fk.name AS ConstraintName,
                    'disabled_foreign_key' AS IssueType
                FROM sys.foreign_keys fk
                JOIN sys.tables tp ON fk.parent_object_id = tp.object_id
                WHERE fk.is_disabled = 1
            """
            
            results = self.db_connector.execute_query_safely(cursor, disabled_fk_query, "Disabled foreign keys check")
            
            integrity_issues = []
            for row in results:
                integrity_issues.append({
                    'table': row[0],
                    'constraint_name': row[1],
                    'issue_type': row[2],
                    'severity': 'medium',
                    'ai_impact': 'Disabled foreign keys may allow data inconsistencies and affect query reliability'
                })
            
            return integrity_issues
            
        except Exception as e:
            logger.warning(f"Could not check referential integrity: {e}")
            return []
