#!/usr/bin/env python3
"""
Utility functions for the Comprehensive Schema Export Tool
"""

import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


def setup_logging(log_dir: Path, log_level: str = 'INFO') -> None:
    """Setup logging configuration with organized directory structure"""
    try:
        # Create logs directory if it doesn't exist
        log_dir.mkdir(exist_ok=True)
        
        # Create daily log file
        log_filename = f"schema_export_{datetime.now().strftime('%Y%m%d')}.log"
        log_file = log_dir / log_filename
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger.info(f"Logging initialized - Log file: {log_file}")
        
    except Exception as e:
        print(f"Failed to setup logging: {e}")
        # Fallback to basic logging
        logging.basicConfig(level=logging.INFO)


def format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def format_duration(seconds: float) -> str:
    """Format duration in human-readable format"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}m {secs}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}h {minutes}m"


def format_number(number: int) -> str:
    """Format number with thousands separators"""
    return f"{number:,}"


def sanitize_filename(filename: str) -> str:
    """Sanitize filename by removing invalid characters"""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename


def get_relative_path(file_path: Path, base_path: Path) -> str:
    """Get relative path from base path, with fallback to absolute path"""
    try:
        return str(file_path.relative_to(base_path))
    except ValueError:
        return str(file_path)


def validate_database_name(database_name: str) -> bool:
    """Validate database name format"""
    if not database_name:
        return False
    
    # Basic validation - no special characters that could cause issues
    invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    return not any(char in database_name for char in invalid_chars)


def validate_server_name(server_name: str) -> bool:
    """Validate server name format"""
    if not server_name:
        return False
    
    # Allow server\instance format
    parts = server_name.split('\\')
    if len(parts) > 2:
        return False
    
    # Basic validation for each part
    for part in parts:
        if not part or any(char in part for char in ['<', '>', ':', '"', '/', '|', '?', '*']):
            return False
    
    return True


def get_environment_info() -> Dict[str, Any]:
    """Get environment information for debugging"""
    try:
        import platform
        import pyodbc
        
        return {
            'python_version': platform.python_version(),
            'platform': platform.platform(),
            'architecture': platform.architecture(),
            'pyodbc_version': pyodbc.version,
            'available_drivers': pyodbc.drivers(),
            'current_directory': str(Path.cwd()),
            'script_directory': str(Path(__file__).parent.absolute())
        }
    except Exception as e:
        logger.warning(f"Could not get environment info: {e}")
        return {'error': str(e)}


def check_disk_space(path: Path, required_mb: int = 100) -> bool:
    """Check if there's enough disk space for export"""
    try:
        if hasattr(os, 'statvfs'):  # Unix/Linux
            statvfs = os.statvfs(path)
            free_bytes = statvfs.f_frsize * statvfs.f_bavail
        else:  # Windows
            import shutil
            free_bytes = shutil.disk_usage(path).free
        
        free_mb = free_bytes / (1024 * 1024)
        return free_mb >= required_mb
        
    except Exception as e:
        logger.warning(f"Could not check disk space: {e}")
        return True  # Assume OK if we can't check


def create_export_summary(export_data: Dict[str, Any]) -> str:
    """Create a formatted export summary"""
    summary_lines = [
        "=" * 70,
        "EXPORT COMPLETED SUCCESSFULLY",
        "=" * 70,
        f"Database: {export_data.get('database', 'Unknown')}",
        f"Server: {export_data.get('server', 'Unknown')}",
        f"Export timestamp: {export_data.get('export_timestamp', 'Unknown')}",
        f"Total tables processed: {export_data.get('total_tables', 0)}",
        "",
        "Organized Directory Structure:",
        f"  📁 Script Root: {export_data.get('script_root', 'Unknown')}",
        f"  📁 Exports: {export_data.get('export_directory', 'Unknown')}",
        f"  📁 Logs: {export_data.get('log_directory', 'Unknown')}",
        "",
        "Files created:"
    ]
    
    # Add file list
    for file_path in export_data.get('file_paths', []):
        try:
            script_root = Path(export_data.get('script_root', ''))
            rel_path = get_relative_path(Path(file_path), script_root)
            summary_lines.append(f"  ✓ {rel_path}")
        except Exception:
            summary_lines.append(f"  ✓ {file_path}")
    
    # Add backup info
    if export_data.get('backup_to_desktop'):
        summary_lines.append("\nBackup copies saved to desktop")
    
    # Add log file info
    log_file = export_data.get('log_file')
    if log_file:
        summary_lines.append(f"\nLog file: {log_file}")
    
    summary_lines.extend([
        "",
        "Schema export completed successfully!",
        "=" * 70
    ])
    
    return "\n".join(summary_lines)


def print_progress_bar(current: int, total: int, prefix: str = '', suffix: str = '', length: int = 50) -> None:
    """Print a progress bar"""
    if total == 0:
        return
    
    percent = (current / total) * 100
    filled_length = int(length * current // total)
    bar = '█' * filled_length + '-' * (length - filled_length)
    
    print(f'\r{prefix} |{bar}| {percent:.1f}% {suffix}', end='', flush=True)
    
    if current == total:
        print()  # New line when complete


def validate_config_file(config_path: str) -> bool:
    """Validate that config file exists and is readable"""
    try:
        config_file = Path(config_path)
        return config_file.exists() and config_file.is_file() and config_file.stat().st_size > 0
    except Exception:
        return False


def get_table_size_category(row_count: int) -> str:
    """Categorize table size for logging and analysis"""
    if row_count <= 1000:
        return "small"
    elif row_count <= 100000:
        return "medium"
    elif row_count <= 1000000:
        return "large"
    else:
        return "very_large"


def estimate_export_time(table_count: int, total_rows: int) -> str:
    """Estimate export time based on table count and rows"""
    # Rough estimates based on typical performance
    base_time = table_count * 2  # 2 seconds per table base
    row_time = total_rows / 10000  # Additional time for large datasets
    
    estimated_seconds = base_time + row_time
    return format_duration(estimated_seconds)


def cleanup_old_exports(export_dir: Path, keep_days: int = 30) -> int:
    """Clean up old export directories"""
    try:
        cutoff_date = datetime.now().timestamp() - (keep_days * 24 * 3600)
        cleaned_count = 0
        
        for item in export_dir.iterdir():
            if item.is_dir() and item.stat().st_mtime < cutoff_date:
                try:
                    import shutil
                    shutil.rmtree(item)
                    cleaned_count += 1
                    logger.info(f"Cleaned up old export directory: {item.name}")
                except Exception as e:
                    logger.warning(f"Could not clean up {item.name}: {e}")
        
        return cleaned_count
        
    except Exception as e:
        logger.warning(f"Could not cleanup old exports: {e}")
        return 0
