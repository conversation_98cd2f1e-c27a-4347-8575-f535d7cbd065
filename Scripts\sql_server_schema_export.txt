--- Tables and Columns ---
TableName: cam.AdvancedSettings, COLUMN_NAME: AdvancedSettingId, DATA_TYPE: int
TableName: cam.AdvancedSettings, COLUMN_NAME: VideoEncoderType, DATA_TYPE: int
TableName: cam.AdvancedSettings, COLUMN_NAME: VideoDecoderType, DATA_TYPE: int
TableName: cam.Camera360Lens, COLUMN_NAME: Camera360LensId, DATA_TYPE: int
TableName: cam.Camera360Lens, COLUMN_NAME: DewarpLensType, DATA_TYPE: int
TableName: cam.Camera360Lens, COLUMN_NAME: DewarpLensOrientationType, DATA_TYPE: int
TableName: cam.Camera360Lens, COLUMN_NAME: DewarpImmervisionLensProfileType, DATA_TYPE: int
TableName: cam.Camera360<PERSON><PERSON>, COLUMN_NAME: DewarpViewType, DATA_TYPE: int
TableName: cam.Camera360Lens, COLUMN_NAME: FishEyeCenterX, DATA_TYPE: int
TableName: cam.Camera360Lens, COLUMN_NAME: FishEyeCenterY, DATA_TYPE: int
TableName: cam.Camera360Lens, COLUMN_NAME: FishEyeRadius, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: CameraGuid, DATA_TYPE: uniqueidentifier
TableName: cam.Cameras, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: VideoDeviceId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: cam.Cameras, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: cam.Cameras, COLUMN_NAME: TimeZone, DATA_TYPE: nvarchar
TableName: cam.Cameras, COLUMN_NAME: CameraNo, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: ChannelNo, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: Latitude, DATA_TYPE: nvarchar
TableName: cam.Cameras, COLUMN_NAME: Longitude, DATA_TYPE: nvarchar
TableName: cam.Cameras, COLUMN_NAME: AlarmPresetType, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: MotionSensitivity, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: AdvancedSettingId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: EdgeSettingId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: RecordingSettingId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: Camera360LensId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: IpSettingId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: StretchSettingId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: PtzSettingId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: StreamProcessingId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: ConeId, DATA_TYPE: int
TableName: cam.Cameras, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: cam.CameraVolumes, COLUMN_NAME: CameraVolumeId, DATA_TYPE: int
TableName: cam.CameraVolumes, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: cam.CameraVolumes, COLUMN_NAME: VolumeId, DATA_TYPE: int
TableName: cam.CamEvents, COLUMN_NAME: CamEventId, DATA_TYPE: int
TableName: cam.CamEvents, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: cam.CamEvents, COLUMN_NAME: CameraEventId, DATA_TYPE: int
TableName: cam.CamEvents, COLUMN_NAME: OnEventType, DATA_TYPE: int
TableName: cam.CamEvents, COLUMN_NAME: OffEventType, DATA_TYPE: int
TableName: cam.CamEvents, COLUMN_NAME: EventAlias, DATA_TYPE: nvarchar
TableName: cam.CamEvents, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: cam.Cones, COLUMN_NAME: ConeId, DATA_TYPE: int
TableName: cam.Cones, COLUMN_NAME: RotateAngle, DATA_TYPE: int
TableName: cam.Cones, COLUMN_NAME: WidthAngle, DATA_TYPE: int
TableName: cam.Cones, COLUMN_NAME: Depth, DATA_TYPE: int
TableName: cam.EdgeSettings, COLUMN_NAME: EdgeSettingId, DATA_TYPE: int
TableName: cam.EdgeSettings, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: cam.EdgeSettings, COLUMN_NAME: EdgeStorageType, DATA_TYPE: int
TableName: cam.EdgeSettings, COLUMN_NAME: WillFormatSdCard, DATA_TYPE: bit
TableName: cam.EdgeSettings, COLUMN_NAME: NasAddress, DATA_TYPE: nvarchar
TableName: cam.EdgeSettings, COLUMN_NAME: NasPath, DATA_TYPE: nvarchar
TableName: cam.EdgeSettings, COLUMN_NAME: NasUserName, DATA_TYPE: nvarchar
TableName: cam.EdgeSettings, COLUMN_NAME: NasPassword, DATA_TYPE: nvarchar
TableName: cam.EdgeSettings, COLUMN_NAME: SyncStartTime, DATA_TYPE: datetime
TableName: cam.EdgeSettings, COLUMN_NAME: SyncInterval, DATA_TYPE: int
TableName: cam.EdgeSettings, COLUMN_NAME: WillSyncServerOffline, DATA_TYPE: bit
TableName: cam.IpSettings, COLUMN_NAME: IpSettingId, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: IpAddress, DATA_TYPE: nvarchar
TableName: cam.IpSettings, COLUMN_NAME: UserName, DATA_TYPE: nvarchar
TableName: cam.IpSettings, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: cam.IpSettings, COLUMN_NAME: Manufacturer, DATA_TYPE: nvarchar
TableName: cam.IpSettings, COLUMN_NAME: Model, DATA_TYPE: nvarchar
TableName: cam.IpSettings, COLUMN_NAME: ProtocolType, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: RttpPort, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: ControlPort, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: StreamPort, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: RtspTcpPort, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: RtspUdpPort, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: HttpPort, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: Path, DATA_TYPE: nvarchar
TableName: cam.IpSettings, COLUMN_NAME: Timeout, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: Retries, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: UseAxisProxy, DATA_TYPE: bit
TableName: cam.IpSettings, COLUMN_NAME: KeepAliveMethodType, DATA_TYPE: int
TableName: cam.IpSettings, COLUMN_NAME: UseAxisStreamProfile, DATA_TYPE: bit
TableName: cam.IpSettings, COLUMN_NAME: AxisStreamProfile, DATA_TYPE: nvarchar
TableName: cam.MediaStreams, COLUMN_NAME: MediaStreamId, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: StreamType, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: StreamName, DATA_TYPE: nvarchar
TableName: cam.MediaStreams, COLUMN_NAME: StreamTokenName, DATA_TYPE: nvarchar
TableName: cam.MediaStreams, COLUMN_NAME: StreamNo, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: MultiStreamNo, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoSourceFormatType, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoSourceFormatProfileType, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoWidth, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoHeight, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoResolution, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoQuality, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoFps, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoGov, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoBitrateControlType, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoAverageBitrate, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoMaximumBitrate, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: IsAudioEnabled, DATA_TYPE: bit
TableName: cam.MediaStreams, COLUMN_NAME: AudioDeviceId, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: AudioChannelNo, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoRecordFormatType, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: VideoLiveFormatType, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: IsScheduledRecordingEnabled, DATA_TYPE: bit
TableName: cam.MediaStreams, COLUMN_NAME: IsAlarmRecordingEnabled, DATA_TYPE: bit
TableName: cam.MediaStreams, COLUMN_NAME: IsMotionRecordingEnabled, DATA_TYPE: bit
TableName: cam.MediaStreams, COLUMN_NAME: IsOnDemandCamera, DATA_TYPE: bit
TableName: cam.MediaStreams, COLUMN_NAME: OnDemandDisconnectTime, DATA_TYPE: int
TableName: cam.MediaStreams, COLUMN_NAME: RecompressionOn, DATA_TYPE: bit
TableName: cam.MotionZones, COLUMN_NAME: MotionZoneId, DATA_TYPE: int
TableName: cam.MotionZones, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: cam.MotionZones, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: cam.MotionZones, COLUMN_NAME: ZoneIndex, DATA_TYPE: int
TableName: cam.MotionZones, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: cam.MotionZones, COLUMN_NAME: PointX, DATA_TYPE: float
TableName: cam.MotionZones, COLUMN_NAME: PointY, DATA_TYPE: float
TableName: cam.MotionZones, COLUMN_NAME: Width, DATA_TYPE: float
TableName: cam.MotionZones, COLUMN_NAME: Height, DATA_TYPE: float
TableName: cam.MotionZones, COLUMN_NAME: Priority, DATA_TYPE: int
TableName: cam.MotionZones, COLUMN_NAME: DoRecSchedule, DATA_TYPE: bit
TableName: cam.Presets, COLUMN_NAME: PresetId, DATA_TYPE: int
TableName: cam.Presets, COLUMN_NAME: PtzSettingId, DATA_TYPE: int
TableName: cam.Presets, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: cam.Presets, COLUMN_NAME: PresetIndex, DATA_TYPE: int
TableName: cam.Presets, COLUMN_NAME: DwellTimeSeconds, DATA_TYPE: int
TableName: cam.Presets, COLUMN_NAME: DoIncludeTour, DATA_TYPE: bit
TableName: cam.ProCamps, COLUMN_NAME: ProCampId, DATA_TYPE: int
TableName: cam.ProCamps, COLUMN_NAME: MediaStreamId, DATA_TYPE: int
TableName: cam.ProCamps, COLUMN_NAME: PropertyType, DATA_TYPE: int
TableName: cam.ProCamps, COLUMN_NAME: Value, DATA_TYPE: bigint
TableName: cam.ProCamps, COLUMN_NAME: Flags, DATA_TYPE: bigint
TableName: cam.PtzSettings, COLUMN_NAME: PtzSettingId, DATA_TYPE: int
TableName: cam.PtzSettings, COLUMN_NAME: IsPtzEnabled, DATA_TYPE: bit
TableName: cam.PtzSettings, COLUMN_NAME: ChannelNo, DATA_TYPE: int
TableName: cam.PtzSettings, COLUMN_NAME: PtzDriver, DATA_TYPE: nvarchar
TableName: cam.PtzSettings, COLUMN_NAME: SerialPort, DATA_TYPE: int
TableName: cam.PtzSettings, COLUMN_NAME: DoFlipDirections, DATA_TYPE: bit
TableName: cam.PtzSettings, COLUMN_NAME: TourResumeTime, DATA_TYPE: time
TableName: cam.PtzSettings, COLUMN_NAME: InactivityTimeoutInterval, DATA_TYPE: time
TableName: cam.PtzSettings, COLUMN_NAME: DoAutoHome, DATA_TYPE: bit
TableName: cam.PtzSettings, COLUMN_NAME: DoPresetTour, DATA_TYPE: bit
TableName: cam.PtzSettings, COLUMN_NAME: DoTourOffOnStart, DATA_TYPE: bit
TableName: cam.PtzSettings, COLUMN_NAME: DoPersistConnection, DATA_TYPE: bit
TableName: cam.PtzSettings, COLUMN_NAME: UseAnalogPtz, DATA_TYPE: bit
TableName: cam.PtzSettings, COLUMN_NAME: HomePreset1, DATA_TYPE: int
TableName: cam.PtzSettings, COLUMN_NAME: HomePreset2, DATA_TYPE: int
TableName: cam.PtzSettings, COLUMN_NAME: HomePreset3, DATA_TYPE: int
TableName: cam.PtzSettings, COLUMN_NAME: HomePreset4, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: RecordingSettingId, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: StorageType, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: SchedFps, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: MotionFps, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: AlarmFps, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: PreMotionSeconds, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: PostMotionSeconds, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: PreAlarmSecords, DATA_TYPE: int
TableName: cam.RecordingSettings, COLUMN_NAME: PostAlarmSeconds, DATA_TYPE: int
TableName: cam.RetentionPolicies, COLUMN_NAME: RetentionPolicyId, DATA_TYPE: int
TableName: cam.RetentionPolicies, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: cam.RetentionPolicies, COLUMN_NAME: PoolType, DATA_TYPE: int
TableName: cam.RetentionPolicies, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: cam.RetentionPolicies, COLUMN_NAME: ActionAfterDays, DATA_TYPE: int
TableName: cam.RetentionPolicies, COLUMN_NAME: MinRetentionDays, DATA_TYPE: int
TableName: cam.RetentionPolicies, COLUMN_NAME: MaxRetentionDays, DATA_TYPE: int
TableName: cam.RetentionPolicies, COLUMN_NAME: StartTime, DATA_TYPE: time
TableName: cam.RetentionPolicies, COLUMN_NAME: ContinuousClips, DATA_TYPE: bit
TableName: cam.RetentionPolicies, COLUMN_NAME: MotionClips, DATA_TYPE: bit
TableName: cam.RetentionPolicies, COLUMN_NAME: AlarmClips, DATA_TYPE: bit
TableName: cam.StreamProcessings, COLUMN_NAME: StreamProcessingId, DATA_TYPE: int
TableName: cam.StreamProcessings, COLUMN_NAME: ExternalCameraNo, DATA_TYPE: int
TableName: cam.StreamProcessings, COLUMN_NAME: IsStreamProcessingEnabled, DATA_TYPE: bit
TableName: cam.StreamProcessings, COLUMN_NAME: IsDftEnabled, DATA_TYPE: bit
TableName: cam.StreamProcessings, COLUMN_NAME: IsDvdEnabled, DATA_TYPE: bit
TableName: cam.StreamProcessings, COLUMN_NAME: WillFavorContinuousVideo, DATA_TYPE: bit
TableName: cam.StreamProcessings, COLUMN_NAME: DoVideoAnalytics, DATA_TYPE: bit
TableName: cam.StreamProcessings, COLUMN_NAME: LatencyDft, DATA_TYPE: int
TableName: cam.StreamProcessings, COLUMN_NAME: GopCountDft, DATA_TYPE: int
TableName: cam.StretchSettings, COLUMN_NAME: StretchSettingId, DATA_TYPE: int
TableName: cam.StretchSettings, COLUMN_NAME: IsDeinterlacingEnabled, DATA_TYPE: bit
TableName: cam.StretchSettings, COLUMN_NAME: IsMotionBasedDeinterlacingEnabled, DATA_TYPE: bit
TableName: cam.StretchSettings, COLUMN_NAME: MotionDeinterlacingThreshold, DATA_TYPE: int
TableName: cam.StretchSettings, COLUMN_NAME: IsMedianFilteringEnabled, DATA_TYPE: bit
TableName: cam.StretchSettings, COLUMN_NAME: IsNoiseReductionEnabled, DATA_TYPE: bit
TableName: cam.StretchSettings, COLUMN_NAME: NoiseReductionLumaStrength, DATA_TYPE: int
TableName: cam.StretchSettings, COLUMN_NAME: NoiseReductionChromaStrength, DATA_TYPE: int
TableName: cam.VideoOverlays, COLUMN_NAME: VideoOverlayId, DATA_TYPE: int
TableName: cam.VideoOverlays, COLUMN_NAME: StreamProcessingId, DATA_TYPE: int
TableName: cam.VideoOverlays, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: cam.VideoOverlays, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: cam.VideoOverlays, COLUMN_NAME: VideoOverlayType, DATA_TYPE: int
TableName: cam.VideoOverlays, COLUMN_NAME: VideoOverlayPositionType, DATA_TYPE: int
TableName: cfg.RegionMembers, COLUMN_NAME: RegionMemberId, DATA_TYPE: int
TableName: cfg.RegionMembers, COLUMN_NAME: RegionHierarchyId, DATA_TYPE: int
TableName: cfg.RegionMembers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: cfg.RegionMembers, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: cfg.RegionsHierarchy, COLUMN_NAME: RegionHierarchyId, DATA_TYPE: int
TableName: cfg.RegionsHierarchy, COLUMN_NAME: RegionGuid, DATA_TYPE: uniqueidentifier
TableName: cfg.RegionsHierarchy, COLUMN_NAME: ParentId, DATA_TYPE: int
TableName: cfg.RegionsHierarchy, COLUMN_NAME: RegionTypeId, DATA_TYPE: int
TableName: cfg.RegionsHierarchy, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: cfg.RegionTypes, COLUMN_NAME: RegionTypeId, DATA_TYPE: int
TableName: cfg.RegionTypes, COLUMN_NAME: Ordinal, DATA_TYPE: int
TableName: cfg.RegionTypes, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: cld.Credentials, COLUMN_NAME: Email, DATA_TYPE: nvarchar
TableName: cld.Credentials, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: cld.Credentials, COLUMN_NAME: Secret, DATA_TYPE: nvarchar
TableName: cld.Service, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: cld.Service, COLUMN_NAME: Port, DATA_TYPE: int
TableName: cld.Service, COLUMN_NAME: IsSecure, DATA_TYPE: bit
TableName: cld.Service, COLUMN_NAME: DefaultTimeout, DATA_TYPE: time
TableName: cld.VpnProfilePush, COLUMN_NAME: VpnProfilePushId, DATA_TYPE: int
TableName: cld.VpnProfilePush, COLUMN_NAME: ServerGuid, DATA_TYPE: uniqueidentifier
TableName: cld.VpnProfilePush, COLUMN_NAME: VpnAddress, DATA_TYPE: nvarchar
TableName: com.ActiveDirectories, COLUMN_NAME: ActiveDirectoryId, DATA_TYPE: int
TableName: com.ActiveDirectories, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: com.ActiveDirectories, COLUMN_NAME: Domain, DATA_TYPE: nvarchar
TableName: com.ActiveDirectories, COLUMN_NAME: BaseDn, DATA_TYPE: nvarchar
TableName: com.ActiveDirectories, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: com.ActiveDirectories, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: com.ActiveDirectories, COLUMN_NAME: SearchNestedDomains, DATA_TYPE: bit
TableName: com.ActiveDirectories, COLUMN_NAME: SearchNestedGroups, DATA_TYPE: bit
TableName: com.ActiveDirectories, COLUMN_NAME: GroupReauthIntervalSecs, DATA_TYPE: int
TableName: com.Certificates, COLUMN_NAME: CertificateId, DATA_TYPE: int
TableName: com.Certificates, COLUMN_NAME: LocalSecureWebServerId, DATA_TYPE: int
TableName: com.Certificates, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: com.Connections, COLUMN_NAME: ConnectionId, DATA_TYPE: int
TableName: com.Connections, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: com.Connections, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: com.Connections, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: com.Connections, COLUMN_NAME: Driver, DATA_TYPE: nvarchar
TableName: com.Connections, COLUMN_NAME: Retries, DATA_TYPE: int
TableName: com.EmailServers, COLUMN_NAME: EmailServerId, DATA_TYPE: int
TableName: com.EmailServers, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: com.EmailServers, COLUMN_NAME: SmtpHostAddress, DATA_TYPE: nvarchar
TableName: com.EmailServers, COLUMN_NAME: SmtpHostPort, DATA_TYPE: int
TableName: com.EmailServers, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: com.EmailServers, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: com.EmailServers, COLUMN_NAME: WillUseTls, DATA_TYPE: bit
TableName: com.EmailServers, COLUMN_NAME: WillAuthenticate, DATA_TYPE: bit
TableName: com.EmailServers, COLUMN_NAME: SenderName, DATA_TYPE: nvarchar
TableName: com.EmailServers, COLUMN_NAME: SenderEmail, DATA_TYPE: nvarchar
TableName: com.LocalSecureWebServers, COLUMN_NAME: LocalSecureWebServerId, DATA_TYPE: int
TableName: com.LocalSecureWebServers, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: com.LocalSecureWebServers, COLUMN_NAME: Port, DATA_TYPE: int
TableName: com.LocalSecureWebServers, COLUMN_NAME: MinTlsVersion, DATA_TYPE: int
TableName: com.LocalWebServers, COLUMN_NAME: LocalWebServerId, DATA_TYPE: int
TableName: com.LocalWebServers, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: com.LocalWebServers, COLUMN_NAME: Port, DATA_TYPE: int
TableName: com.Ports, COLUMN_NAME: PortId, DATA_TYPE: int
TableName: com.Ports, COLUMN_NAME: DataPort, DATA_TYPE: int
TableName: com.Ports, COLUMN_NAME: SecureDataPort, DATA_TYPE: int
TableName: com.Ports, COLUMN_NAME: AdminPort, DATA_TYPE: int
TableName: com.Ports, COLUMN_NAME: UdpMediaPortMin, DATA_TYPE: int
TableName: com.Ports, COLUMN_NAME: UdpMediaPortMax, DATA_TYPE: int
TableName: com.Ports, COLUMN_NAME: RtspPortIsEnabled, DATA_TYPE: bit
TableName: com.Ports, COLUMN_NAME: RtspPort, DATA_TYPE: int
TableName: com.Ports, COLUMN_NAME: RtspUseBasicAuth, DATA_TYPE: bit
TableName: cs.CommonSettings, COLUMN_NAME: CommonSettingId, DATA_TYPE: int
TableName: cs.CommonSettings, COLUMN_NAME: Settings, DATA_TYPE: nvarchar
TableName: cs.LoggingComponents, COLUMN_NAME: LoggingComponentType, DATA_TYPE: int
TableName: cs.LoggingComponents, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: cs.LoggingComponents, COLUMN_NAME: Identifier, DATA_TYPE: nvarchar
TableName: cs.LoggingComponents, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: cso.BandwidthControls, COLUMN_NAME: BandwidthControlId, DATA_TYPE: int
TableName: cso.BandwidthControls, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: cso.BandwidthControls, COLUMN_NAME: IsOnDemandEventsChecked, DATA_TYPE: bit
TableName: cso.BandwidthControls, COLUMN_NAME: StatusPeriodMs, DATA_TYPE: int
TableName: dbo.Migrations, COLUMN_NAME: MigrationId, DATA_TYPE: int
TableName: dbo.Migrations, COLUMN_NAME: SQL, DATA_TYPE: nvarchar
TableName: dbo.Migrations, COLUMN_NAME: FileName, DATA_TYPE: nvarchar
TableName: dbo.Migrations, COLUMN_NAME: AppliedDate, DATA_TYPE: datetime
TableName: dbo.Migrations, COLUMN_NAME: SqlHash, DATA_TYPE: int
TableName: grp.GroupMembers, COLUMN_NAME: GroupMemberId, DATA_TYPE: int
TableName: grp.GroupMembers, COLUMN_NAME: GroupId, DATA_TYPE: int
TableName: grp.GroupMembers, COLUMN_NAME: UserId, DATA_TYPE: int
TableName: grp.Groups, COLUMN_NAME: GroupId, DATA_TYPE: int
TableName: grp.Groups, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: grp.Groups, COLUMN_NAME: ParentId, DATA_TYPE: int
TableName: grp.Groups, COLUMN_NAME: ActiveDirectoryId, DATA_TYPE: int
TableName: grp.Groups, COLUMN_NAME: SubDomain, DATA_TYPE: nvarchar
TableName: grp.Groups, COLUMN_NAME: GroupGuid, DATA_TYPE: uniqueidentifier
TableName: grp.Groups, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: grp.Groups, COLUMN_NAME: IsAdmin, DATA_TYPE: bit
TableName: grp.Groups, COLUMN_NAME: RemoteAccess, DATA_TYPE: bit
TableName: grp.Groups, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: grp.Groups, COLUMN_NAME: PtzPriority, DATA_TYPE: int
TableName: grp.Groups, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: grp.Groups, COLUMN_NAME: MsOwnerGuid, DATA_TYPE: uniqueidentifier
TableName: lic.Licenses, COLUMN_NAME: LicenseId, DATA_TYPE: int
TableName: lic.Licenses, COLUMN_NAME: LicenseType, DATA_TYPE: int
TableName: lic.Licenses, COLUMN_NAME: IpLicensesPurchased, DATA_TYPE: int
TableName: lic.Licenses, COLUMN_NAME: AnalogLicensesPurchased, DATA_TYPE: int
TableName: lic.Licenses, COLUMN_NAME: FeatureKeysJson, DATA_TYPE: nvarchar
TableName: ms.BackupConfigs, COLUMN_NAME: BackupConfigId, DATA_TYPE: int
TableName: ms.BackupConfigs, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: ms.BackupConfigs, COLUMN_NAME: IsDefault, DATA_TYPE: bit
TableName: ms.BackupConfigs, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: ms.BackupConfigs, COLUMN_NAME: CronExpression, DATA_TYPE: nvarchar
TableName: ms.BackupConfigs, COLUMN_NAME: BackupPath, DATA_TYPE: nvarchar
TableName: ms.BackupConfigs, COLUMN_NAME: BackupTimestampFormat, DATA_TYPE: nvarchar
TableName: ms.BackupConfigs, COLUMN_NAME: BackupOptions, DATA_TYPE: nvarchar
TableName: ms.DicoveredServers, COLUMN_NAME: DicoveredServerId, DATA_TYPE: int
TableName: ms.DicoveredServers, COLUMN_NAME: IpAddress, DATA_TYPE: nvarchar
TableName: ms.DicoveredServers, COLUMN_NAME: FriendlyName, DATA_TYPE: nvarchar
TableName: ms.DicoveredServers, COLUMN_NAME: ServerGuid, DATA_TYPE: uniqueidentifier
TableName: ms.DicoveredServers, COLUMN_NAME: LastUpdated, DATA_TYPE: datetime
TableName: ms.FederatedChildren, COLUMN_NAME: Guid, DATA_TYPE: uniqueidentifier
TableName: ms.FederatedChildren, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: ms.FederatedChildren, COLUMN_NAME: Version, DATA_TYPE: nvarchar
TableName: ms.FederatedChildren, COLUMN_NAME: UseTls, DATA_TYPE: bit
TableName: ms.FederatedChildren, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: ms.FederatedChildren, COLUMN_NAME: Port, DATA_TYPE: int
TableName: ms.FederatedChildren, COLUMN_NAME: Identity, DATA_TYPE: nvarchar
TableName: ms.FederatedChildren, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: ms.FederatedChildren, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: ms.FederatedChildren, COLUMN_NAME: WriteToken, DATA_TYPE: uniqueidentifier
TableName: ms.FederatedChildren, COLUMN_NAME: RecordingServersCount, DATA_TYPE: int
TableName: ms.FederatedChildren, COLUMN_NAME: CameraLicensesPurchased, DATA_TYPE: int
TableName: ms.FederatedChildren, COLUMN_NAME: CameraLicensesUsed, DATA_TYPE: int
TableName: ms.FederatedParent, COLUMN_NAME: Guid, DATA_TYPE: uniqueidentifier
TableName: ms.FederatedParent, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: ms.FederatedParent, COLUMN_NAME: Version, DATA_TYPE: nvarchar
TableName: ms.FederatedParent, COLUMN_NAME: UseTls, DATA_TYPE: bit
TableName: ms.FederatedParent, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: ms.FederatedParent, COLUMN_NAME: Port, DATA_TYPE: int
TableName: ms.FederatedParent, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: ms.FederatedParent, COLUMN_NAME: WriteToken, DATA_TYPE: uniqueidentifier
TableName: ms.FederatedSiblings, COLUMN_NAME: Guid, DATA_TYPE: uniqueidentifier
TableName: ms.FederatedSiblings, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: ms.FederatedSiblings, COLUMN_NAME: Version, DATA_TYPE: nvarchar
TableName: ms.FederatedSiblings, COLUMN_NAME: UseTls, DATA_TYPE: bit
TableName: ms.FederatedSiblings, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: ms.FederatedSiblings, COLUMN_NAME: Port, DATA_TYPE: int
TableName: ms.FederationSettings, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: ms.Identity, COLUMN_NAME: Guid, DATA_TYPE: uniqueidentifier
TableName: ms.Identity, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: ms.Identity, COLUMN_NAME: HostAddressesJson, DATA_TYPE: nvarchar
TableName: ms.Identity, COLUMN_NAME: BaseUrl, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: ImportRecordingServerQueueId, DATA_TYPE: int
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: FriendlyName, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: SvrCtrlPortNo, DATA_TYPE: int
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: RestPortNo, DATA_TYPE: int
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: UseTls, DATA_TYPE: bit
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: AutoProvision, DATA_TYPE: bit
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: ConnectionTimeoutMs, DATA_TYPE: int
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: AddUserId, DATA_TYPE: int
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: EnqueuedAt, DATA_TYPE: datetime
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: RegionHierarchyId, DATA_TYPE: int
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: PostActions, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: ImportRecordingServerStatusId, DATA_TYPE: int
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: FriendlyName, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: SvrCtrlPortNo, DATA_TYPE: int
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: RestPortNo, DATA_TYPE: int
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: UseTls, DATA_TYPE: bit
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: AutoProvision, DATA_TYPE: bit
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: AddUserId, DATA_TYPE: int
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: RegionHierarchyId, DATA_TYPE: int
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: EnqueuedAt, DATA_TYPE: datetime
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: ImportStatus, DATA_TYPE: int
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: StartedAt, DATA_TYPE: datetime
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: EndedAt, DATA_TYPE: datetime
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: Elapsed, DATA_TYPE: time
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: ErrorMessage, DATA_TYPE: nvarchar
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: PostActions, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ImportServerConfigQueueGuid, DATA_TYPE: uniqueidentifier
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: AutoProvision, DATA_TYPE: bit
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: FriendlyName, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ServerGuid, DATA_TYPE: uniqueidentifier
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: Version, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: UseTls, DATA_TYPE: bit
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: Platform, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: SerialNo, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: EnqueuedAt, DATA_TYPE: datetime
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ServerTimeZoneInfo, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: EnvironmentInfo, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ServerConfigRoot, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: LicenseInfo, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: ImportServerConfigStatusGuid, DATA_TYPE: uniqueidentifier
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: ServerGuid, DATA_TYPE: uniqueidentifier
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: StartedAt, DATA_TYPE: datetime
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: EndedAt, DATA_TYPE: datetime
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: Elapsed, DATA_TYPE: time
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: WasSuccessful, DATA_TYPE: bit
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: Message, DATA_TYPE: nvarchar
TableName: ms.NamedResources, COLUMN_NAME: NamedResourceId, DATA_TYPE: int
TableName: ms.NamedResources, COLUMN_NAME: ResourceType, DATA_TYPE: int
TableName: ms.NamedResources, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: ms.NamedResources, COLUMN_NAME: Description, DATA_TYPE: nvarchar
TableName: ms.NamedResources, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: ms.NamedResources, COLUMN_NAME: SecondaryAddress, DATA_TYPE: nvarchar
TableName: ms.PushServerStatus, COLUMN_NAME: PushServerStatusId, DATA_TYPE: int
TableName: ms.PushServerStatus, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: ms.PushServerStatus, COLUMN_NAME: PushToServerId, DATA_TYPE: int
TableName: ms.PushServerStatus, COLUMN_NAME: PushDevices, DATA_TYPE: bit
TableName: ms.PushServerStatus, COLUMN_NAME: PushUsers, DATA_TYPE: bit
TableName: ms.PushServerStatus, COLUMN_NAME: PushConfiguration, DATA_TYPE: bit
TableName: ms.PushServerStatus, COLUMN_NAME: CorsWhitelist, DATA_TYPE: nvarchar
TableName: ms.PushServerStatus, COLUMN_NAME: TimeStamp, DATA_TYPE: datetime
TableName: ms.PushServerStatus, COLUMN_NAME: WasSuccessful, DATA_TYPE: bit
TableName: ms.PushServerStatus, COLUMN_NAME: ErrorMessage, DATA_TYPE: nvarchar
TableName: ms.PushServerStatus, COLUMN_NAME: PushMediaEnc, DATA_TYPE: bit
TableName: ms.PushToServers, COLUMN_NAME: PushToServerId, DATA_TYPE: int
TableName: ms.PushToServers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: ms.PushToServers, COLUMN_NAME: PushDevices, DATA_TYPE: bit
TableName: ms.PushToServers, COLUMN_NAME: PushUsers, DATA_TYPE: bit
TableName: ms.PushToServers, COLUMN_NAME: PushConfiguration, DATA_TYPE: bit
TableName: ms.PushToServers, COLUMN_NAME: CorsWhitelist, DATA_TYPE: nvarchar
TableName: ms.PushToServers, COLUMN_NAME: TimeStamp, DATA_TYPE: datetime
TableName: ms.PushToServers, COLUMN_NAME: HttpPort, DATA_TYPE: int
TableName: ms.PushToServers, COLUMN_NAME: SecureHttpPort, DATA_TYPE: int
TableName: ms.PushToServers, COLUMN_NAME: Priority, DATA_TYPE: int
TableName: ms.PushToServers, COLUMN_NAME: PushMediaEnc, DATA_TYPE: bit
TableName: ms.VersionInfo, COLUMN_NAME: VersionInfoId, DATA_TYPE: int
TableName: ms.VersionInfo, COLUMN_NAME: Major, DATA_TYPE: int
TableName: ms.VersionInfo, COLUMN_NAME: Minor, DATA_TYPE: int
TableName: ms.VersionInfo, COLUMN_NAME: Build, DATA_TYPE: int
TableName: ms.VersionInfo, COLUMN_NAME: Revision, DATA_TYPE: int
TableName: ms.VersionInfo, COLUMN_NAME: TimeStamp, DATA_TYPE: datetime
TableName: rs.DefaultBehaviors, COLUMN_NAME: DefaultBehaviorId, DATA_TYPE: int
TableName: rs.DefaultBehaviors, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.DefaultBehaviors, COLUMN_NAME: PacketLossMaxAcceptPercent, DATA_TYPE: int
TableName: rs.DefaultBehaviors, COLUMN_NAME: PacketLossReportIntervalSec, DATA_TYPE: int
TableName: rs.DefaultBehaviors, COLUMN_NAME: IsCameraAccessEnabled, DATA_TYPE: bit
TableName: rs.EventNotifications, COLUMN_NAME: EventNotificationId, DATA_TYPE: int
TableName: rs.EventNotifications, COLUMN_NAME: EmailSubject, DATA_TYPE: nvarchar
TableName: rs.EventNotifications, COLUMN_NAME: EmailRecipients, DATA_TYPE: nvarchar
TableName: rs.EventNotifications, COLUMN_NAME: SmsSubject, DATA_TYPE: nvarchar
TableName: rs.EventNotifications, COLUMN_NAME: SmsRecipients, DATA_TYPE: nvarchar
TableName: rs.EventNotifications, COLUMN_NAME: SmsGateway, DATA_TYPE: nvarchar
TableName: rs.EventNotificationSettings, COLUMN_NAME: EventNotificationSettingId, DATA_TYPE: int
TableName: rs.EventNotificationSettings, COLUMN_NAME: EventNotificationId, DATA_TYPE: int
TableName: rs.EventNotificationSettings, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.EventNotificationSettings, COLUMN_NAME: SourceType, DATA_TYPE: int
TableName: rs.EventNotificationSettings, COLUMN_NAME: EventType, DATA_TYPE: int
TableName: rs.EventNotificationSettings, COLUMN_NAME: WillIncludeSnapshot, DATA_TYPE: bit
TableName: rs.EventNotificationSettings, COLUMN_NAME: MinInterval, DATA_TYPE: int
TableName: rs.EventNotificationSettings, COLUMN_NAME: ConfirmationWaitFor, DATA_TYPE: int
TableName: rs.EventNotificationSettings, COLUMN_NAME: ConfirmationWaitEnabled, DATA_TYPE: bit
TableName: rs.EventsToLog, COLUMN_NAME: EventToLogId, DATA_TYPE: int
TableName: rs.EventsToLog, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.EventsToLog, COLUMN_NAME: EventType, DATA_TYPE: int
TableName: rs.EventTriggerActions, COLUMN_NAME: EventTriggerActionId, DATA_TYPE: int
TableName: rs.EventTriggerActions, COLUMN_NAME: EventTriggerId, DATA_TYPE: int
TableName: rs.EventTriggerActions, COLUMN_NAME: EventTriggerActionGuid, DATA_TYPE: uniqueidentifier
TableName: rs.EventTriggerActions, COLUMN_NAME: ActionType, DATA_TYPE: int
TableName: rs.EventTriggerActions, COLUMN_NAME: ActionValue, DATA_TYPE: int
TableName: rs.EventTriggerActions, COLUMN_NAME: PriorityType, DATA_TYPE: int
TableName: rs.EventTriggerActions, COLUMN_NAME: IsPrimaryAction, DATA_TYPE: bit
TableName: rs.EventTriggerActions, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: rs.EventTriggerActions, COLUMN_NAME: IOTriggerId, DATA_TYPE: int
TableName: rs.EventTriggerActions, COLUMN_NAME: VolumeId, DATA_TYPE: int
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerResponseActionId, DATA_TYPE: int
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerId, DATA_TYPE: int
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerResponseId, DATA_TYPE: int
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerActionId, DATA_TYPE: int
TableName: rs.EventTriggerResponses, COLUMN_NAME: EventTriggerResponseId, DATA_TYPE: int
TableName: rs.EventTriggerResponses, COLUMN_NAME: EventTriggerId, DATA_TYPE: int
TableName: rs.EventTriggerResponses, COLUMN_NAME: IsAckRequired, DATA_TYPE: bit
TableName: rs.EventTriggerResponses, COLUMN_NAME: IsResetRequired, DATA_TYPE: bit
TableName: rs.EventTriggerResponses, COLUMN_NAME: CanForward, DATA_TYPE: bit
TableName: rs.EventTriggers, COLUMN_NAME: EventTriggerId, DATA_TYPE: int
TableName: rs.EventTriggers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.EventTriggers, COLUMN_NAME: EventTriggerResponseId, DATA_TYPE: int
TableName: rs.EventTriggers, COLUMN_NAME: EventTriggerGuid, DATA_TYPE: uniqueidentifier
TableName: rs.EventTriggers, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.EventTriggers, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rs.EventTriggers, COLUMN_NAME: SetIOTrigger, DATA_TYPE: bit
TableName: rs.EventTriggers, COLUMN_NAME: PriorityType, DATA_TYPE: int
TableName: rs.EventTriggers, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rs.EventTriggerSources, COLUMN_NAME: EventTriggerSourceId, DATA_TYPE: int
TableName: rs.EventTriggerSources, COLUMN_NAME: EventTriggerId, DATA_TYPE: int
TableName: rs.EventTriggerSources, COLUMN_NAME: EventSourceGuid, DATA_TYPE: uniqueidentifier
TableName: rs.EventTriggerSources, COLUMN_NAME: EventType, DATA_TYPE: int
TableName: rs.EventTriggerSources, COLUMN_NAME: EventValue, DATA_TYPE: int
TableName: rs.EventTriggerSources, COLUMN_NAME: IsPrimaryEvent, DATA_TYPE: bit
TableName: rs.EventTriggerSources, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: rs.EventTriggerSources, COLUMN_NAME: IOTriggerId, DATA_TYPE: int
TableName: rs.EventTriggerSources, COLUMN_NAME: VolumeId, DATA_TYPE: int
TableName: rs.EventTriggerSources, COLUMN_NAME: UserId, DATA_TYPE: int
TableName: rs.IoInputs, COLUMN_NAME: IoInputId, DATA_TYPE: int
TableName: rs.IoInputs, COLUMN_NAME: IOTriggerId, DATA_TYPE: int
TableName: rs.IoInputs, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.IoInputs, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rs.IoInputs, COLUMN_NAME: PinNo, DATA_TYPE: int
TableName: rs.IoInputs, COLUMN_NAME: TriggerStateType, DATA_TYPE: int
TableName: rs.IoInputs, COLUMN_NAME: EventType, DATA_TYPE: int
TableName: rs.IoOutputCameras, COLUMN_NAME: IoOutputCameraId, DATA_TYPE: int
TableName: rs.IoOutputCameras, COLUMN_NAME: IoTriggerId, DATA_TYPE: int
TableName: rs.IoOutputCameras, COLUMN_NAME: IoOutputId, DATA_TYPE: int
TableName: rs.IoOutputCameras, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: rs.IoOutputs, COLUMN_NAME: IoOutputId, DATA_TYPE: int
TableName: rs.IoOutputs, COLUMN_NAME: IOTriggerId, DATA_TYPE: int
TableName: rs.IoOutputs, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.IoOutputs, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rs.IoOutputs, COLUMN_NAME: PinNo, DATA_TYPE: int
TableName: rs.IoOutputs, COLUMN_NAME: TriggerStateType, DATA_TYPE: int
TableName: rs.IoOutputs, COLUMN_NAME: ResetTimeMs, DATA_TYPE: int
TableName: rs.IoOutputs, COLUMN_NAME: DeviceType, DATA_TYPE: int
TableName: rs.IoOutputs, COLUMN_NAME: WillAutoReset, DATA_TYPE: bit
TableName: rs.IOTriggers, COLUMN_NAME: IOTriggerId, DATA_TYPE: int
TableName: rs.IOTriggers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.IOTriggers, COLUMN_NAME: IODeviceId, DATA_TYPE: int
TableName: rs.IOTriggers, COLUMN_NAME: IOTriggerGuid, DATA_TYPE: uniqueidentifier
TableName: rs.IOTriggers, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.IOTriggers, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rs.IOTriggers, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: rs.IOTriggers, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: rs.IOTriggers, COLUMN_NAME: DriverName, DATA_TYPE: nvarchar
TableName: rs.IOTriggers, COLUMN_NAME: IpAddress, DATA_TYPE: nvarchar
TableName: rs.IOTriggers, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rs.Loggers, COLUMN_NAME: LoggerId, DATA_TYPE: int
TableName: rs.Loggers, COLUMN_NAME: LogSettingId, DATA_TYPE: int
TableName: rs.Loggers, COLUMN_NAME: ComponentNo, DATA_TYPE: int
TableName: rs.Loggers, COLUMN_NAME: PriorityNo, DATA_TYPE: int
TableName: rs.Loggers, COLUMN_NAME: DoFlushOnWrite, DATA_TYPE: bit
TableName: rs.Loggers, COLUMN_NAME: DoRolloverOnSaveCfg, DATA_TYPE: bit
TableName: rs.Loggers, COLUMN_NAME: KeepForDays, DATA_TYPE: int
TableName: rs.LogSettings, COLUMN_NAME: LogSettingId, DATA_TYPE: int
TableName: rs.LogSettings, COLUMN_NAME: LogFilePath, DATA_TYPE: nvarchar
TableName: rs.MediaEncryptions, COLUMN_NAME: ServerGuid, DATA_TYPE: uniqueidentifier
TableName: rs.MediaEncryptions, COLUMN_NAME: Enabled, DATA_TYPE: bit
TableName: rs.MediaEncryptions, COLUMN_NAME: CipherMode, DATA_TYPE: int
TableName: rs.MediaEncryptions, COLUMN_NAME: KeySize, DATA_TYPE: int
TableName: rs.MediaEncryptions, COLUMN_NAME: PasswordBase64, DATA_TYPE: nvarchar
TableName: rs.MediaEncryptions, COLUMN_NAME: Iterations, DATA_TYPE: int
TableName: rs.MediaEncryptions, COLUMN_NAME: HashAlgorithm, DATA_TYPE: int
TableName: rs.MediaEncryptions, COLUMN_NAME: KekBase64, DATA_TYPE: nvarchar
TableName: rs.MediaEncryptions, COLUMN_NAME: DekBase64, DATA_TYPE: nvarchar
TableName: rs.MediaEncryptions, COLUMN_NAME: DekCreatedOnUtc, DATA_TYPE: datetime
TableName: rs.MediaEncryptions, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rs.MediaEncryptions, COLUMN_NAME: WriteToken, DATA_TYPE: uniqueidentifier
TableName: rs.NVRCameras, COLUMN_NAME: NVRCameraId, DATA_TYPE: int
TableName: rs.NVRCameras, COLUMN_NAME: NVRId, DATA_TYPE: int
TableName: rs.NVRCameras, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: rs.NVRCameras, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.NVRCameras, COLUMN_NAME: ChannelNo, DATA_TYPE: int
TableName: rs.NVRCameras, COLUMN_NAME: IsVmsVolume, DATA_TYPE: bit
TableName: rs.NVRCameras, COLUMN_NAME: IsNvrStorage, DATA_TYPE: bit
TableName: rs.NVRs, COLUMN_NAME: NVRId, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: VideoDeviceId, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: NVRGuid, DATA_TYPE: uniqueidentifier
TableName: rs.NVRs, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.NVRs, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rs.NVRs, COLUMN_NAME: Manufacturer, DATA_TYPE: nvarchar
TableName: rs.NVRs, COLUMN_NAME: Model, DATA_TYPE: nvarchar
TableName: rs.NVRs, COLUMN_NAME: ChannelCount, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: TimeZone, DATA_TYPE: nvarchar
TableName: rs.NVRs, COLUMN_NAME: HttpPort, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: RtspPort, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: ControlPort, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: HostAddress, DATA_TYPE: nvarchar
TableName: rs.NVRs, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: rs.NVRs, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: rs.NVRs, COLUMN_NAME: TimeoutSecs, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: NumRetries, DATA_TYPE: int
TableName: rs.NVRs, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rs.PresetZones, COLUMN_NAME: PresetZoneId, DATA_TYPE: int
TableName: rs.PresetZones, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.PresetZones, COLUMN_NAME: PriorityType, DATA_TYPE: int
TableName: rs.PresetZones, COLUMN_NAME: HoldTimeSeconds, DATA_TYPE: int
TableName: rs.PresetZones, COLUMN_NAME: DwellTimeSeconds, DATA_TYPE: int
TableName: rs.PresetZones, COLUMN_NAME: IsCyclingEnabled, DATA_TYPE: bit
TableName: rs.RecordingServers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: RecordingServerGuid, DATA_TYPE: uniqueidentifier
TableName: rs.RecordingServers, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.RecordingServers, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: Description, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: TimeZone, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: Version, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: ProductId, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: UseTls, DATA_TYPE: bit
TableName: rs.RecordingServers, COLUMN_NAME: IpLicensesUsed, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: AnalogLicensesUsed, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: IsLegacy, DATA_TYPE: bit
TableName: rs.RecordingServers, COLUMN_NAME: Platform, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: SerialNumber, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: WasAutoProvisioned, DATA_TYPE: bit
TableName: rs.RecordingServers, COLUMN_NAME: LastSync, DATA_TYPE: datetime
TableName: rs.RecordingServers, COLUMN_NAME: FeatureKeyVersion, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: Latitude, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: Longitude, DATA_TYPE: nvarchar
TableName: rs.RecordingServers, COLUMN_NAME: PortId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: ConnectionId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: LicenseId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: LocalWebServerId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: LocalSecureWebServerId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: LogSettingId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: EventNotificationId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: UdpBroadcastId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: PrimaryRecordingServerFailoverConfigId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: StandbyRecordingServerFailoverConfigId, DATA_TYPE: int
TableName: rs.RecordingServers, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rs.UdpBroadcasts, COLUMN_NAME: UdpBroadcastId, DATA_TYPE: int
TableName: rs.UdpBroadcasts, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.UdpBroadcasts, COLUMN_NAME: Port, DATA_TYPE: int
TableName: rs.UdpBroadcasts, COLUMN_NAME: RepeatCount, DATA_TYPE: int
TableName: rs.UdpBroadcasts, COLUMN_NAME: WaitTimeoutMs, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: VideoDecoderId, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: VideoCompressionType, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: TranscoderEngineType, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: VideoCompressionProfileType, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: LatencyType, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: VideoCompressionRateType, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: BitRate, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: GopLength, DATA_TYPE: int
TableName: rs.VideoDecoders, COLUMN_NAME: HardwareAccelerationType, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: VideoEncoderId, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: VideoCompressionType, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: TranscoderEngineType, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: VideoCompressionProfileType, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: LatencyType, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: VideoCompressionRateType, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: BitRate, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: GopLength, DATA_TYPE: int
TableName: rs.VideoEncoders, COLUMN_NAME: HardwareAccelerationType, DATA_TYPE: int
TableName: rs.VolumeExpirations, COLUMN_NAME: VolumeExpirationId, DATA_TYPE: int
TableName: rs.VolumeExpirations, COLUMN_NAME: VolumeId, DATA_TYPE: int
TableName: rs.VolumeExpirations, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.VolumeExpirations, COLUMN_NAME: StartTime, DATA_TYPE: time
TableName: rs.VolumeExpirations, COLUMN_NAME: Frequency, DATA_TYPE: int
TableName: rs.VolumeExpirations, COLUMN_NAME: MaxVideoDays, DATA_TYPE: int
TableName: rs.VolumeExpirations, COLUMN_NAME: IsScheduleClips, DATA_TYPE: bit
TableName: rs.VolumeExpirations, COLUMN_NAME: IsMotionClips, DATA_TYPE: bit
TableName: rs.VolumeExpirations, COLUMN_NAME: IsAlarmClips, DATA_TYPE: bit
TableName: rs.VolumeExports, COLUMN_NAME: VolumeExportId, DATA_TYPE: int
TableName: rs.VolumeExports, COLUMN_NAME: VolumeId, DATA_TYPE: int
TableName: rs.VolumeExports, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.VolumeExports, COLUMN_NAME: StartTime, DATA_TYPE: time
TableName: rs.VolumeExports, COLUMN_NAME: Frequency, DATA_TYPE: int
TableName: rs.VolumeExports, COLUMN_NAME: WillStoreAllExports, DATA_TYPE: bit
TableName: rs.VolumeExports, COLUMN_NAME: WillStoreServerExportQueue, DATA_TYPE: bit
TableName: rs.VolumeRetentions, COLUMN_NAME: VolumeRetentionId, DATA_TYPE: int
TableName: rs.VolumeRetentions, COLUMN_NAME: VolumeId, DATA_TYPE: int
TableName: rs.VolumeRetentions, COLUMN_NAME: ArchiveVolumeId, DATA_TYPE: int
TableName: rs.VolumeRetentions, COLUMN_NAME: BackupVolumeId, DATA_TYPE: int
TableName: rs.VolumeRetentions, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.VolumeRetentions, COLUMN_NAME: StartTime, DATA_TYPE: time
TableName: rs.VolumeRetentions, COLUMN_NAME: Frequency, DATA_TYPE: int
TableName: rs.VolumeRetentions, COLUMN_NAME: RetentionDays, DATA_TYPE: int
TableName: rs.VolumeRetentions, COLUMN_NAME: IsScheduleClips, DATA_TYPE: bit
TableName: rs.VolumeRetentions, COLUMN_NAME: IsMotionClips, DATA_TYPE: bit
TableName: rs.VolumeRetentions, COLUMN_NAME: IsAlarmClips, DATA_TYPE: bit
TableName: rs.Volumes, COLUMN_NAME: VolumeId, DATA_TYPE: int
TableName: rs.Volumes, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rs.Volumes, COLUMN_NAME: VolumeGuid, DATA_TYPE: uniqueidentifier
TableName: rs.Volumes, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rs.Volumes, COLUMN_NAME: VolumeType, DATA_TYPE: int
TableName: rs.Volumes, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rs.Volumes, COLUMN_NAME: Path, DATA_TYPE: nvarchar
TableName: rs.Volumes, COLUMN_NAME: UncPath, DATA_TYPE: nvarchar
TableName: rs.Volumes, COLUMN_NAME: FreeSpacePercentage, DATA_TYPE: int
TableName: rs.Volumes, COLUMN_NAME: DesiredMinVideoDays, DATA_TYPE: int
TableName: rs.Volumes, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rsd.AudioDevices, COLUMN_NAME: AudioDeviceId, DATA_TYPE: int
TableName: rsd.AudioDevices, COLUMN_NAME: DeviceGuid, DATA_TYPE: uniqueidentifier
TableName: rsd.AudioDevices, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rsd.AudioDevices, COLUMN_NAME: DriverType, DATA_TYPE: int
TableName: rsd.AudioDevices, COLUMN_NAME: ConnectionType, DATA_TYPE: int
TableName: rsd.AudioDevices, COLUMN_NAME: InternalNo, DATA_TYPE: int
TableName: rsd.AudioDevices, COLUMN_NAME: DeviceAttributes, DATA_TYPE: bigint
TableName: rsd.AudioDevices, COLUMN_NAME: StatusType, DATA_TYPE: int
TableName: rsd.AudioDevices, COLUMN_NAME: WasAutoDetected, DATA_TYPE: bit
TableName: rsd.AudioDevices, COLUMN_NAME: LastSync, DATA_TYPE: datetime
TableName: rsd.AudioDevices, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rsd.AudioDevices, COLUMN_NAME: Manufacturer, DATA_TYPE: nvarchar
TableName: rsd.AudioDevices, COLUMN_NAME: Model, DATA_TYPE: nvarchar
TableName: rsd.AudioDevices, COLUMN_NAME: Quality, DATA_TYPE: int
TableName: rsd.AudioDevices, COLUMN_NAME: ChannelMode, DATA_TYPE: int
TableName: rsd.AudioDevices, COLUMN_NAME: IpV4Address, DATA_TYPE: nvarchar
TableName: rsd.AudioDevices, COLUMN_NAME: IpV6Address, DATA_TYPE: nvarchar
TableName: rsd.AudioDevices, COLUMN_NAME: ComPort, DATA_TYPE: int
TableName: rsd.AudioDeviceServers, COLUMN_NAME: AudioDeviceServerId, DATA_TYPE: int
TableName: rsd.AudioDeviceServers, COLUMN_NAME: AudioDeviceId, DATA_TYPE: int
TableName: rsd.AudioDeviceServers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsd.CameraEvents, COLUMN_NAME: CameraEventId, DATA_TYPE: int
TableName: rsd.CameraEvents, COLUMN_NAME: VideoDeviceId, DATA_TYPE: int
TableName: rsd.CameraEvents, COLUMN_NAME: CameraEventType, DATA_TYPE: int
TableName: rsd.CameraEvents, COLUMN_NAME: Category, DATA_TYPE: nvarchar
TableName: rsd.CameraEvents, COLUMN_NAME: EventName, DATA_TYPE: nvarchar
TableName: rsd.CameraEvents, COLUMN_NAME: SubEventName, DATA_TYPE: nvarchar
TableName: rsd.CameraEvents, COLUMN_NAME: OnEventType, DATA_TYPE: int
TableName: rsd.CameraEvents, COLUMN_NAME: OffEventType, DATA_TYPE: int
TableName: rsd.CameraEvents, COLUMN_NAME: IsPulsed, DATA_TYPE: bit
TableName: rsd.DeviceTriggers, COLUMN_NAME: DeviceTriggerId, DATA_TYPE: int
TableName: rsd.DeviceTriggers, COLUMN_NAME: VideoDeviceId, DATA_TYPE: int
TableName: rsd.DeviceTriggers, COLUMN_NAME: IsInput, DATA_TYPE: bit
TableName: rsd.DeviceTriggers, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rsd.DeviceTriggers, COLUMN_NAME: IdleState, DATA_TYPE: int
TableName: rsd.DiskDevices, COLUMN_NAME: DiskDeviceId, DATA_TYPE: int
TableName: rsd.DiskDevices, COLUMN_NAME: DeviceGuid, DATA_TYPE: uniqueidentifier
TableName: rsd.DiskDevices, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rsd.DiskDevices, COLUMN_NAME: DriverType, DATA_TYPE: int
TableName: rsd.DiskDevices, COLUMN_NAME: ConnectionType, DATA_TYPE: int
TableName: rsd.DiskDevices, COLUMN_NAME: InternalNo, DATA_TYPE: int
TableName: rsd.DiskDevices, COLUMN_NAME: DeviceAttributes, DATA_TYPE: bigint
TableName: rsd.DiskDevices, COLUMN_NAME: StatusType, DATA_TYPE: int
TableName: rsd.DiskDevices, COLUMN_NAME: WasAutoDetected, DATA_TYPE: bit
TableName: rsd.DiskDevices, COLUMN_NAME: LastSync, DATA_TYPE: datetime
TableName: rsd.DiskDevices, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rsd.DiskDevices, COLUMN_NAME: TotalSizeMB, DATA_TYPE: bigint
TableName: rsd.DiskDevices, COLUMN_NAME: FreeSpaceMB, DATA_TYPE: bigint
TableName: rsd.DiskDevices, COLUMN_NAME: IsIndexingEnabled, DATA_TYPE: bit
TableName: rsd.DiskDeviceServers, COLUMN_NAME: DiskDeviceServerId, DATA_TYPE: int
TableName: rsd.DiskDeviceServers, COLUMN_NAME: DiskDeviceId, DATA_TYPE: int
TableName: rsd.DiskDeviceServers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: IODeviceId, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: DeviceGuid, DATA_TYPE: uniqueidentifier
TableName: rsd.IODevices, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rsd.IODevices, COLUMN_NAME: DriverType, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: ConnectionType, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: InternalNo, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: DeviceAttributes, DATA_TYPE: bigint
TableName: rsd.IODevices, COLUMN_NAME: StatusType, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: WasAutoDetected, DATA_TYPE: bit
TableName: rsd.IODevices, COLUMN_NAME: LastSync, DATA_TYPE: datetime
TableName: rsd.IODevices, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rsd.IODevices, COLUMN_NAME: ComPort, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: InputCount, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: OutputCount, DATA_TYPE: int
TableName: rsd.IODevices, COLUMN_NAME: Manufacturer, DATA_TYPE: nvarchar
TableName: rsd.IODevices, COLUMN_NAME: Model, DATA_TYPE: nvarchar
TableName: rsd.IODevices, COLUMN_NAME: IpV4Address, DATA_TYPE: nvarchar
TableName: rsd.IODevices, COLUMN_NAME: IpV6Address, DATA_TYPE: nvarchar
TableName: rsd.IODeviceServers, COLUMN_NAME: IODeviceServerId, DATA_TYPE: int
TableName: rsd.IODeviceServers, COLUMN_NAME: IODeviceId, DATA_TYPE: int
TableName: rsd.IODeviceServers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MediaProfileId, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: VideoDeviceId, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: ChannelNo, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rsd.MediaProfiles, COLUMN_NAME: Token, DATA_TYPE: nvarchar
TableName: rsd.MediaProfiles, COLUMN_NAME: SnapshotUrl, DATA_TYPE: nvarchar
TableName: rsd.MediaProfiles, COLUMN_NAME: StreamUrl, DATA_TYPE: nvarchar
TableName: rsd.MediaProfiles, COLUMN_NAME: EncodingProfile, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: VideoEncoderType, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxWidth, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxHeight, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: Resolutions, DATA_TYPE: nvarchar
TableName: rsd.MediaProfiles, COLUMN_NAME: MinGovLength, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxGovLength, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: GovLength, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MinFps, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxFps, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: Fps, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MinQuality, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxQuality, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: Quality, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MinBitrate, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxBitrate, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: Bitrate, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: IsAudioAvailable, DATA_TYPE: bit
TableName: rsd.MediaProfiles, COLUMN_NAME: AudioEncodingType, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: AudioBitrate, DATA_TYPE: int
TableName: rsd.MediaProfiles, COLUMN_NAME: AudioSampleRate, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: SerialDeviceId, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: DeviceGuid, DATA_TYPE: uniqueidentifier
TableName: rsd.SerialDevices, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rsd.SerialDevices, COLUMN_NAME: DriverType, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: ConnectionType, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: InternalNo, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: DeviceAttributes, DATA_TYPE: bigint
TableName: rsd.SerialDevices, COLUMN_NAME: StatusType, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: WasAutoDetected, DATA_TYPE: bit
TableName: rsd.SerialDevices, COLUMN_NAME: LastSync, DATA_TYPE: datetime
TableName: rsd.SerialDevices, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rsd.SerialDevices, COLUMN_NAME: PortNo, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: BaudRate, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: DataBits, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: ParityType, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: StopBits, DATA_TYPE: int
TableName: rsd.SerialDevices, COLUMN_NAME: FlowControl, DATA_TYPE: int
TableName: rsd.SerialDeviceServers, COLUMN_NAME: SerialDeviceServerId, DATA_TYPE: int
TableName: rsd.SerialDeviceServers, COLUMN_NAME: SerialDeviceId, DATA_TYPE: int
TableName: rsd.SerialDeviceServers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: VideoDeviceId, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: ProviderServerId, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: DeviceGuid, DATA_TYPE: uniqueidentifier
TableName: rsd.VideoDevices, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: DriverType, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: ConnectionType, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: InternalNo, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: DeviceAttributes, DATA_TYPE: bigint
TableName: rsd.VideoDevices, COLUMN_NAME: StatusType, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: WasAutoDetected, DATA_TYPE: bit
TableName: rsd.VideoDevices, COLUMN_NAME: LastSync, DATA_TYPE: datetime
TableName: rsd.VideoDevices, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: MacAddress, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: SerialNumber, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: Manufacturer, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: Model, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: IsPtz, DATA_TYPE: bit
TableName: rsd.VideoDevices, COLUMN_NAME: ChannelCount, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: PresetCount, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: IpV4Address, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: IpV6Address, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: HttpPort, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: HttpsPort, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: RtspPort, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: Firmware, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: VideoStandardType, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: SourceFilePath, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: OnvifDeviceUrl, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: OnvifPtzUrl, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: OnvifEventUrl, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: StretchSdkVersion, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: StretchDriverVersion, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: StretchFirmwareVersion, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: StretchBootLoaderVersion, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: StretchBspVersion, DATA_TYPE: nvarchar
TableName: rsd.VideoDevices, COLUMN_NAME: StretchPciSlotNumber, DATA_TYPE: int
TableName: rsd.VideoDevices, COLUMN_NAME: StretchCardIsPresent, DATA_TYPE: bit
TableName: rsd.VideoDevices, COLUMN_NAME: OnBoardEventsLastSync, DATA_TYPE: datetime
TableName: rsd.VideoDevices, COLUMN_NAME: SupportsAudio, DATA_TYPE: bit
TableName: rsd.VideoDevices, COLUMN_NAME: VideoCompressionsArr, DATA_TYPE: nvarchar
TableName: rsd.VideoDeviceServers, COLUMN_NAME: VideoDeviceServerId, DATA_TYPE: int
TableName: rsd.VideoDeviceServers, COLUMN_NAME: VideoDeviceId, DATA_TYPE: int
TableName: rsd.VideoDeviceServers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsf.PrimariesStandbyServers, COLUMN_NAME: PrimaryStandbyServerId, DATA_TYPE: int
TableName: rsf.PrimariesStandbyServers, COLUMN_NAME: PrimaryRecordingServerId, DATA_TYPE: int
TableName: rsf.PrimariesStandbyServers, COLUMN_NAME: StandbyRecordingServerId, DATA_TYPE: int
TableName: rsf.PrimaryRecordingServerFailoverConfigs, COLUMN_NAME: PrimaryRecordingServerFailoverConfigId, DATA_TYPE: int
TableName: rsf.PrimaryRecordingServerFailoverConfigs, COLUMN_NAME: FailoverAfterMS, DATA_TYPE: int
TableName: rsf.PrimaryRecordingServerFailoverConfigs, COLUMN_NAME: RestoreAfterMS, DATA_TYPE: int
TableName: rsf.StandbyRecordingServerFailoverConfigs, COLUMN_NAME: StandbyRecordingServerFailoverConfigId, DATA_TYPE: int
TableName: rsf.StandbyRecordingServerFailoverConfigs, COLUMN_NAME: PrimaryRecordingServerId, DATA_TYPE: int
TableName: rsf.StandbyRecordingServerFailoverConfigs, COLUMN_NAME: SharePath, DATA_TYPE: nvarchar
TableName: rsi.AgentVis, COLUMN_NAME: AgentViId, DATA_TYPE: int
TableName: rsi.AgentVis, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsi.AgentVis, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rsi.AgentVis, COLUMN_NAME: EngineAddress, DATA_TYPE: nvarchar
TableName: rsi.AgentVis, COLUMN_NAME: ProxyLocalPort, DATA_TYPE: int
TableName: rsi.AxisOneClicks, COLUMN_NAME: AxisOneClickId, DATA_TYPE: int
TableName: rsi.AxisOneClicks, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsi.AxisOneClicks, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rsi.AxisOneClicks, COLUMN_NAME: DispatchServerAddress, DATA_TYPE: nvarchar
TableName: rsi.AxisOneClicks, COLUMN_NAME: UserName, DATA_TYPE: nvarchar
TableName: rsi.AxisOneClicks, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: rsi.AxisOneClicks, COLUMN_NAME: ProxyExternalAddress, DATA_TYPE: nvarchar
TableName: rsi.AxisOneClicks, COLUMN_NAME: ProxyExternalPort, DATA_TYPE: int
TableName: rsi.AxisOneClicks, COLUMN_NAME: ProxyInternalAddress, DATA_TYPE: nvarchar
TableName: rsi.AxisOneClicks, COLUMN_NAME: ProxyInternalPort, DATA_TYPE: int
TableName: rsi.AxisOneClicks, COLUMN_NAME: OcccVersion, DATA_TYPE: int
TableName: rsi.AxisOneClicks, COLUMN_NAME: CertificatePath, DATA_TYPE: nvarchar
TableName: rsi.Bolds, COLUMN_NAME: BoldId, DATA_TYPE: int
TableName: rsi.Bolds, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsi.Bolds, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rsi.Bolds, COLUMN_NAME: ManitouServerAddress, DATA_TYPE: nvarchar
TableName: rsi.Bolds, COLUMN_NAME: ManitouPort, DATA_TYPE: int
TableName: rsi.Bolds, COLUMN_NAME: HeartbeatInterval, DATA_TYPE: int
TableName: rsi.BriefCams, COLUMN_NAME: BriefCamId, DATA_TYPE: int
TableName: rsi.BriefCams, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsi.BriefCams, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rsi.BriefCams, COLUMN_NAME: Address, DATA_TYPE: nvarchar
TableName: rsi.BriefCams, COLUMN_NAME: Port, DATA_TYPE: int
TableName: rsi.BriefCams, COLUMN_NAME: UserName, DATA_TYPE: nvarchar
TableName: rsi.BriefCams, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: rsi.BriefCams, COLUMN_NAME: AdminPath, DATA_TYPE: nvarchar
TableName: rsi.BriefCams, COLUMN_NAME: UserPath, DATA_TYPE: nvarchar
TableName: rsi.S2s, COLUMN_NAME: S2Id, DATA_TYPE: int
TableName: rsi.S2s, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsi.S2s, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rsi.S2s, COLUMN_NAME: S2ControllerAddress, DATA_TYPE: nvarchar
TableName: rsi.SureViews, COLUMN_NAME: SureViewId, DATA_TYPE: int
TableName: rsi.SureViews, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsi.SureViews, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rsi.SureViews, COLUMN_NAME: ImmixDeviceNumber, DATA_TYPE: int
TableName: rsi.SureViews, COLUMN_NAME: ImmixSmtpServer, DATA_TYPE: nvarchar
TableName: rsi.SureViews, COLUMN_NAME: ImmixSmtpPort, DATA_TYPE: int
TableName: rsi.SureViews, COLUMN_NAME: ImmixNumberOfSnapshots, DATA_TYPE: int
TableName: rsi.SureViews, COLUMN_NAME: ImmixDurationSeconds, DATA_TYPE: int
TableName: rsp.StorageDrives, COLUMN_NAME: StoragePoolGuid, DATA_TYPE: uniqueidentifier
TableName: rsp.StorageDrives, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsp.StorageDrives, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: rsp.StorageDrives, COLUMN_NAME: DrivePath, DATA_TYPE: nvarchar
TableName: rsp.StorageDrives, COLUMN_NAME: VideoSpacePercent, DATA_TYPE: int
TableName: rsp.StorageDrives, COLUMN_NAME: IsOverflow, DATA_TYPE: bit
TableName: rsp.StoragePools, COLUMN_NAME: StoragePoolGuid, DATA_TYPE: uniqueidentifier
TableName: rsp.StoragePools, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsp.StoragePools, COLUMN_NAME: PoolType, DATA_TYPE: int
TableName: rsp.StoragePools, COLUMN_NAME: WarningLevelPercent, DATA_TYPE: int
TableName: rsp.StoragePoolSettings, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: rsp.StoragePoolSettings, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: shl.CameraSchedules, COLUMN_NAME: CameraScheduleId, DATA_TYPE: int
TableName: shl.CameraSchedules, COLUMN_NAME: ScheduleId, DATA_TYPE: int
TableName: shl.CameraSchedules, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: shl.EventTriggerSchedules, COLUMN_NAME: EventTriggerScheduleId, DATA_TYPE: int
TableName: shl.EventTriggerSchedules, COLUMN_NAME: ScheduleId, DATA_TYPE: int
TableName: shl.EventTriggerSchedules, COLUMN_NAME: EventTriggerId, DATA_TYPE: int
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: ScheduledTimeSegmentId, DATA_TYPE: int
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: ScheduleId, DATA_TYPE: int
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: ScheduleOperationId, DATA_TYPE: int
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: OnDate, DATA_TYPE: date
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: DayOfWeekType, DATA_TYPE: int
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: StartTime, DATA_TYPE: time
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: EndTime, DATA_TYPE: time
TableName: shl.ScheduleOperations, COLUMN_NAME: ScheduleOperationId, DATA_TYPE: int
TableName: shl.ScheduleOperations, COLUMN_NAME: ScheduleId, DATA_TYPE: int
TableName: shl.ScheduleOperations, COLUMN_NAME: OperationType, DATA_TYPE: int
TableName: shl.Schedules, COLUMN_NAME: ScheduleId, DATA_TYPE: int
TableName: shl.Schedules, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: shl.Schedules, COLUMN_NAME: ScheduleGuid, DATA_TYPE: uniqueidentifier
TableName: shl.Schedules, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: shl.Schedules, COLUMN_NAME: ResourceType, DATA_TYPE: int
TableName: shl.Schedules, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: shl.Schedules, COLUMN_NAME: IsDefault, DATA_TYPE: bit
TableName: shl.Schedules, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: tmp.TmpGuids, COLUMN_NAME: Guid, DATA_TYPE: uniqueidentifier
TableName: tmp.TmpGuids, COLUMN_NAME: Value, DATA_TYPE: uniqueidentifier
TableName: tmp.TmpInts, COLUMN_NAME: Id, DATA_TYPE: uniqueidentifier
TableName: tmp.TmpInts, COLUMN_NAME: IntValue, DATA_TYPE: int
TableName: tmp.TmpStrings, COLUMN_NAME: Guid, DATA_TYPE: uniqueidentifier
TableName: tmp.TmpStrings, COLUMN_NAME: Value, DATA_TYPE: nvarchar
TableName: usr.Configurations, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.Configurations, COLUMN_NAME: IsAppAutoLoginChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsAppCustomFavoritesChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsAppServersCamerasChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsAppServerIpAddressChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsAppAutoFullscreenChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsPtzChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsLvAutoSequenceChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsLvVideoPanelChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsLvEventPanelChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsLvMapPanelChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsLvWallPanelChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsMaxTileCountEnabled, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: MaxTileCount, DATA_TYPE: int
TableName: usr.Configurations, COLUMN_NAME: IsAvMapPanelChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsAvCameraPanelChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: PlayersType, DATA_TYPE: int
TableName: usr.Configurations, COLUMN_NAME: IsDbUserConnectionsChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsDbSearchVideoChecked, DATA_TYPE: bit
TableName: usr.Configurations, COLUMN_NAME: IsDbSearchEventsChecked, DATA_TYPE: bit
TableName: usr.PasswordHistory, COLUMN_NAME: PasswordHistoryId, DATA_TYPE: int
TableName: usr.PasswordHistory, COLUMN_NAME: UserId, DATA_TYPE: int
TableName: usr.PasswordHistory, COLUMN_NAME: SetOn, DATA_TYPE: datetime
TableName: usr.PasswordHistory, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: usr.PasswordPolicies, COLUMN_NAME: PasswordPolicyId, DATA_TYPE: int
TableName: usr.PasswordPolicies, COLUMN_NAME: PolicyJson, DATA_TYPE: nvarchar
TableName: usr.Roles, COLUMN_NAME: RoleId, DATA_TYPE: int
TableName: usr.Roles, COLUMN_NAME: Identifier, DATA_TYPE: nvarchar
TableName: usr.Roles, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: usr.UserCameras, COLUMN_NAME: UserCameraId, DATA_TYPE: int
TableName: usr.UserCameras, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserCameras, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: usr.UserCameras, COLUMN_NAME: IsLiveChecked, DATA_TYPE: bit
TableName: usr.UserCameras, COLUMN_NAME: IsPlaybackChecked, DATA_TYPE: bit
TableName: usr.UserCameras, COLUMN_NAME: IsAudioChecked, DATA_TYPE: bit
TableName: usr.UserCameras, COLUMN_NAME: IsExportChecked, DATA_TYPE: bit
TableName: usr.UserCameras, COLUMN_NAME: IsSnapshotChecked, DATA_TYPE: bit
TableName: usr.UserCameras, COLUMN_NAME: IsLightChecked, DATA_TYPE: bit
TableName: usr.UserCameras, COLUMN_NAME: IsPtzChecked, DATA_TYPE: bit
TableName: usr.UserCameras, COLUMN_NAME: PlaybackTimeout, DATA_TYPE: int
TableName: usr.UserCameras, COLUMN_NAME: IsFavorite, DATA_TYPE: bit
TableName: usr.UserEvents, COLUMN_NAME: UserEventId, DATA_TYPE: int
TableName: usr.UserEvents, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserEvents, COLUMN_NAME: EventType, DATA_TYPE: int
TableName: usr.UserEvents, COLUMN_NAME: IsChecked, DATA_TYPE: bit
TableName: usr.UserEventTriggers, COLUMN_NAME: UserEventTriggerId, DATA_TYPE: int
TableName: usr.UserEventTriggers, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserEventTriggers, COLUMN_NAME: EventTriggerId, DATA_TYPE: int
TableName: usr.UserEventTriggers, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: usr.UserEventTriggers, COLUMN_NAME: WillExecute, DATA_TYPE: bit
TableName: usr.UserEventTriggers, COLUMN_NAME: IsFavorite, DATA_TYPE: bit
TableName: usr.UserInputs, COLUMN_NAME: UserInputId, DATA_TYPE: int
TableName: usr.UserInputs, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserInputs, COLUMN_NAME: IOInputId, DATA_TYPE: int
TableName: usr.UserInputs, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: usr.UserInputs, COLUMN_NAME: IsFavorite, DATA_TYPE: bit
TableName: usr.UserModules, COLUMN_NAME: UserModuleId, DATA_TYPE: int
TableName: usr.UserModules, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserModules, COLUMN_NAME: ModuleType, DATA_TYPE: int
TableName: usr.UserModules, COLUMN_NAME: HasAccess, DATA_TYPE: bit
TableName: usr.UserModules, COLUMN_NAME: IsSetAtStartup, DATA_TYPE: bit
TableName: usr.UserOutputs, COLUMN_NAME: UserOutputId, DATA_TYPE: int
TableName: usr.UserOutputs, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserOutputs, COLUMN_NAME: IOOutputId, DATA_TYPE: int
TableName: usr.UserOutputs, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: usr.UserOutputs, COLUMN_NAME: WillExecute, DATA_TYPE: bit
TableName: usr.UserOutputs, COLUMN_NAME: IsFavorite, DATA_TYPE: bit
TableName: usr.UserPasswordPolicies, COLUMN_NAME: UserId, DATA_TYPE: int
TableName: usr.UserPasswordPolicies, COLUMN_NAME: PolicyJson, DATA_TYPE: nvarchar
TableName: usr.UserPresets, COLUMN_NAME: UserPresetId, DATA_TYPE: int
TableName: usr.UserPresets, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserPresets, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: usr.UserPresets, COLUMN_NAME: PresetIndex, DATA_TYPE: int
TableName: usr.UserPresets, COLUMN_NAME: IsSetChecked, DATA_TYPE: bit
TableName: usr.UserPresets, COLUMN_NAME: IsShowChecked, DATA_TYPE: bit
TableName: usr.UserRoles, COLUMN_NAME: UserRoleId, DATA_TYPE: int
TableName: usr.UserRoles, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserRoles, COLUMN_NAME: RoleId, DATA_TYPE: int
TableName: usr.Users, COLUMN_NAME: UserId, DATA_TYPE: int
TableName: usr.Users, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.Users, COLUMN_NAME: UserGuid, DATA_TYPE: uniqueidentifier
TableName: usr.Users, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: usr.Users, COLUMN_NAME: IsAdmin, DATA_TYPE: bit
TableName: usr.Users, COLUMN_NAME: RemoteAccess, DATA_TYPE: bit
TableName: usr.Users, COLUMN_NAME: ActiveDirectoryId, DATA_TYPE: int
TableName: usr.Users, COLUMN_NAME: SubDomain, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: Username, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: Password, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: IsDefaultPassword, DATA_TYPE: bit
TableName: usr.Users, COLUMN_NAME: PtzPriority, DATA_TYPE: int
TableName: usr.Users, COLUMN_NAME: IsManaged, DATA_TYPE: bit
TableName: usr.Users, COLUMN_NAME: ExpiresOn, DATA_TYPE: datetime
TableName: usr.Users, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: LastName, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: Email, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: ResetOn, DATA_TYPE: datetime
TableName: usr.Users, COLUMN_NAME: FirstName, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: Phone, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: Address, DATA_TYPE: nvarchar
TableName: usr.Users, COLUMN_NAME: Cloud, DATA_TYPE: bit
TableName: usr.Users, COLUMN_NAME: MsOwnerGuid, DATA_TYPE: uniqueidentifier
TableName: usr.UserServers, COLUMN_NAME: UserServerId, DATA_TYPE: int
TableName: usr.UserServers, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserServers, COLUMN_NAME: RecordingServerId, DATA_TYPE: int
TableName: usr.UserServers, COLUMN_NAME: IsAdminChecked, DATA_TYPE: bit
TableName: usr.UserServers, COLUMN_NAME: IsApiChecked, DATA_TYPE: bit
TableName: usr.UserServers, COLUMN_NAME: IsEventsChecked, DATA_TYPE: bit
TableName: usr.UserServers, COLUMN_NAME: PtzPriorityType, DATA_TYPE: int
TableName: usr.UserServers, COLUMN_NAME: IsFavorite, DATA_TYPE: bit
TableName: usr.UserViews, COLUMN_NAME: UserViewId, DATA_TYPE: int
TableName: usr.UserViews, COLUMN_NAME: ConfigurationId, DATA_TYPE: int
TableName: usr.UserViews, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: usr.UserViews, COLUMN_NAME: IsFavorite, DATA_TYPE: bit
TableName: usr.UserViews, COLUMN_NAME: Navigation, DATA_TYPE: bit
TableName: usr.UserViews, COLUMN_NAME: ShowUrl, DATA_TYPE: bit
TableName: usr.UserViews, COLUMN_NAME: LiveView, DATA_TYPE: bit
TableName: usr.UserViews, COLUMN_NAME: Playback, DATA_TYPE: bit
TableName: vw.CameraViews, COLUMN_NAME: CameraViewId, DATA_TYPE: int
TableName: vw.CameraViews, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.CameraViews, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: vw.CameraViews, COLUMN_NAME: FitOption, DATA_TYPE: int
TableName: vw.CameraViewTriggers, COLUMN_NAME: CameraViewTriggerId, DATA_TYPE: int
TableName: vw.CameraViewTriggers, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.CameraViewTriggers, COLUMN_NAME: CameraViewId, DATA_TYPE: int
TableName: vw.CameraViewTriggers, COLUMN_NAME: IoOutputId, DATA_TYPE: int
TableName: vw.FixedTiles, COLUMN_NAME: FixedTileId, DATA_TYPE: int
TableName: vw.FixedTiles, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.FixedTiles, COLUMN_NAME: TileId, DATA_TYPE: int
TableName: vw.FixedTiles, COLUMN_NAME: PositionX, DATA_TYPE: float
TableName: vw.FixedTiles, COLUMN_NAME: PositionY, DATA_TYPE: float
TableName: vw.FixedTiles, COLUMN_NAME: Width, DATA_TYPE: float
TableName: vw.FixedTiles, COLUMN_NAME: Height, DATA_TYPE: float
TableName: vw.MapImages, COLUMN_NAME: MapImageId, DATA_TYPE: int
TableName: vw.MapImages, COLUMN_NAME: ImageBytes, DATA_TYPE: varbinary
TableName: vw.MapImages, COLUMN_NAME: CompressionType, DATA_TYPE: int
TableName: vw.MapImages, COLUMN_NAME: FileSize, DATA_TYPE: int
TableName: vw.MapImages, COLUMN_NAME: Md5, DATA_TYPE: nvarchar
TableName: vw.MapImages, COLUMN_NAME: Height, DATA_TYPE: int
TableName: vw.MapImages, COLUMN_NAME: Width, DATA_TYPE: int
TableName: vw.Maps, COLUMN_NAME: MapId, DATA_TYPE: int
TableName: vw.Maps, COLUMN_NAME: MapImageId, DATA_TYPE: int
TableName: vw.Maps, COLUMN_NAME: MapGuid, DATA_TYPE: uniqueidentifier
TableName: vw.Maps, COLUMN_NAME: MapType, DATA_TYPE: int
TableName: vw.Maps, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: vw.Maps, COLUMN_NAME: Address, DATA_TYPE: nvarchar
TableName: vw.Maps, COLUMN_NAME: Latitude, DATA_TYPE: nvarchar
TableName: vw.Maps, COLUMN_NAME: Longitude, DATA_TYPE: nvarchar
TableName: vw.Maps, COLUMN_NAME: CompressionType, DATA_TYPE: int
TableName: vw.Maps, COLUMN_NAME: FileSize, DATA_TYPE: int
TableName: vw.Maps, COLUMN_NAME: Md5, DATA_TYPE: nvarchar
TableName: vw.Maps, COLUMN_NAME: Height, DATA_TYPE: int
TableName: vw.Maps, COLUMN_NAME: Width, DATA_TYPE: int
TableName: vw.MapViewCameras, COLUMN_NAME: MapViewCameraId, DATA_TYPE: int
TableName: vw.MapViewCameras, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.MapViewCameras, COLUMN_NAME: MapViewId, DATA_TYPE: int
TableName: vw.MapViewCameras, COLUMN_NAME: CameraId, DATA_TYPE: int
TableName: vw.MapViewCameras, COLUMN_NAME: PositionX, DATA_TYPE: float
TableName: vw.MapViewCameras, COLUMN_NAME: PositionY, DATA_TYPE: float
TableName: vw.MapViewLinks, COLUMN_NAME: MapViewLinkId, DATA_TYPE: int
TableName: vw.MapViewLinks, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.MapViewLinks, COLUMN_NAME: MapViewId, DATA_TYPE: int
TableName: vw.MapViewLinks, COLUMN_NAME: LinkedViewId, DATA_TYPE: int
TableName: vw.MapViewLinks, COLUMN_NAME: PositionX, DATA_TYPE: float
TableName: vw.MapViewLinks, COLUMN_NAME: PositionY, DATA_TYPE: float
TableName: vw.MapViews, COLUMN_NAME: MapViewId, DATA_TYPE: int
TableName: vw.MapViews, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.MapViews, COLUMN_NAME: MapId, DATA_TYPE: int
TableName: vw.Pins, COLUMN_NAME: PinId, DATA_TYPE: int
TableName: vw.Pins, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.Pins, COLUMN_NAME: MapViewId, DATA_TYPE: int
TableName: vw.Pins, COLUMN_NAME: Latitude, DATA_TYPE: nvarchar
TableName: vw.Pins, COLUMN_NAME: Longitude, DATA_TYPE: nvarchar
TableName: vw.Pins, COLUMN_NAME: PositionX, DATA_TYPE: float
TableName: vw.Pins, COLUMN_NAME: PositionY, DATA_TYPE: float
TableName: vw.ResponsiveTiles, COLUMN_NAME: ResponsiveTileId, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: TileId, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: ParentTileId, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: LayoutStyle, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: Ordinal, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: AbsoluteWidth, DATA_TYPE: float
TableName: vw.ResponsiveTiles, COLUMN_NAME: AbsoluteHeight, DATA_TYPE: float
TableName: vw.ResponsiveTiles, COLUMN_NAME: PositionX, DATA_TYPE: float
TableName: vw.ResponsiveTiles, COLUMN_NAME: PositionY, DATA_TYPE: float
TableName: vw.ResponsiveTiles, COLUMN_NAME: Row, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: Column, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: RowSpan, DATA_TYPE: int
TableName: vw.ResponsiveTiles, COLUMN_NAME: ColumnSpan, DATA_TYPE: int
TableName: vw.Tiles, COLUMN_NAME: TileId, DATA_TYPE: int
TableName: vw.Tiles, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.Tiles, COLUMN_NAME: CameraViewId, DATA_TYPE: int
TableName: vw.Tiles, COLUMN_NAME: MapViewId, DATA_TYPE: int
TableName: vw.Tiles, COLUMN_NAME: WebViewId, DATA_TYPE: int
TableName: vw.Tiles, COLUMN_NAME: WallViewId, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: RelatedViewId, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: ViewGuid, DATA_TYPE: uniqueidentifier
TableName: vw.Views, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: vw.Views, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: vw.Views, COLUMN_NAME: IsTemplate, DATA_TYPE: bit
TableName: vw.Views, COLUMN_NAME: DeclaredType, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: LayoutStyle, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: Width, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: Height, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: CreatedByUserId, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: AspectRatio, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: IsInTour, DATA_TYPE: bit
TableName: vw.Views, COLUMN_NAME: DwellTimeSeconds, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: Fps, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: Quality, DATA_TYPE: int
TableName: vw.Views, COLUMN_NAME: ShowCones, DATA_TYPE: bit
TableName: vw.Views, COLUMN_NAME: ShowLabel, DATA_TYPE: bit
TableName: vw.Views, COLUMN_NAME: ScaleToFit, DATA_TYPE: bit
TableName: vw.Views, COLUMN_NAME: ZoomLevel, DATA_TYPE: float
TableName: vw.Views, COLUMN_NAME: PreviewIcon, DATA_TYPE: varbinary
TableName: vw.Views, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: vw.WallViews, COLUMN_NAME: WallViewId, DATA_TYPE: int
TableName: vw.WallViews, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.WallViews, COLUMN_NAME: WvAgentDisplayId, DATA_TYPE: int
TableName: vw.WallViews, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: vw.WallViews, COLUMN_NAME: AspectRatio, DATA_TYPE: int
TableName: vw.WallViews, COLUMN_NAME: RowCount, DATA_TYPE: int
TableName: vw.WallViews, COLUMN_NAME: ColumnCount, DATA_TYPE: int
TableName: vw.WebViews, COLUMN_NAME: WebViewId, DATA_TYPE: int
TableName: vw.WebViews, COLUMN_NAME: ViewId, DATA_TYPE: int
TableName: vw.WebViews, COLUMN_NAME: WebUrl, DATA_TYPE: nvarchar
TableName: vw.WebViews, COLUMN_NAME: IsAudioEnabled, DATA_TYPE: bit
TableName: vwt.TemplateTiles, COLUMN_NAME: TemplateTileId, DATA_TYPE: int
TableName: vwt.TemplateTiles, COLUMN_NAME: ViewTemplateId, DATA_TYPE: int
TableName: vwt.TemplateTiles, COLUMN_NAME: ParentTemplateTileId, DATA_TYPE: int
TableName: vwt.TemplateTiles, COLUMN_NAME: TileType, DATA_TYPE: int
TableName: vwt.TemplateTiles, COLUMN_NAME: PointX, DATA_TYPE: float
TableName: vwt.TemplateTiles, COLUMN_NAME: PointY, DATA_TYPE: float
TableName: vwt.TemplateTiles, COLUMN_NAME: Height, DATA_TYPE: float
TableName: vwt.TemplateTiles, COLUMN_NAME: Width, DATA_TYPE: float
TableName: vwt.TemplateTiles, COLUMN_NAME: Row, DATA_TYPE: int
TableName: vwt.TemplateTiles, COLUMN_NAME: Column, DATA_TYPE: int
TableName: vwt.TemplateTiles, COLUMN_NAME: RowSpan, DATA_TYPE: int
TableName: vwt.TemplateTiles, COLUMN_NAME: ColumnSpan, DATA_TYPE: int
TableName: vwt.ViewTemplates, COLUMN_NAME: ViewTemplateId, DATA_TYPE: int
TableName: vwt.ViewTemplates, COLUMN_NAME: ViewTemplateGuid, DATA_TYPE: uniqueidentifier
TableName: vwt.ViewTemplates, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: vwt.ViewTemplates, COLUMN_NAME: ViewType, DATA_TYPE: int
TableName: vwt.ViewTemplates, COLUMN_NAME: LayoutStyle, DATA_TYPE: int
TableName: vwt.ViewTemplates, COLUMN_NAME: ImageBytesBase64, DATA_TYPE: nvarchar
TableName: wall.WallViewAgents, COLUMN_NAME: WallViewAgentId, DATA_TYPE: int
TableName: wall.WallViewAgents, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: wall.WallViewAgents, COLUMN_NAME: AgentGuid, DATA_TYPE: uniqueidentifier
TableName: wall.WallViewAgents, COLUMN_NAME: Name, DATA_TYPE: nvarchar
TableName: wall.WallViewAgents, COLUMN_NAME: HostIp, DATA_TYPE: nvarchar
TableName: wall.WallViewAgents, COLUMN_NAME: PortNo, DATA_TYPE: int
TableName: wall.WallViewAgents, COLUMN_NAME: Note, DATA_TYPE: nvarchar
TableName: wall.WvAgentDisplays, COLUMN_NAME: WvAgentDisplayId, DATA_TYPE: int
TableName: wall.WvAgentDisplays, COLUMN_NAME: WallViewAgentId, DATA_TYPE: int
TableName: wall.WvAgentDisplays, COLUMN_NAME: IsEnabled, DATA_TYPE: bit
TableName: wall.WvAgentDisplays, COLUMN_NAME: IsPrimary, DATA_TYPE: bit
TableName: wall.WvAgentDisplays, COLUMN_NAME: DisplayNo, DATA_TYPE: int
TableName: wall.WvAgentDisplays, COLUMN_NAME: DeviceName, DATA_TYPE: nvarchar
TableName: wall.WvAgentDisplays, COLUMN_NAME: DisplayName, DATA_TYPE: nvarchar

--- Primary Keys ---
TableName: cam.AdvancedSettings, COLUMN_NAME: AdvancedSettingId
TableName: cam.Camera360Lens, COLUMN_NAME: Camera360LensId
TableName: cam.Cameras, COLUMN_NAME: CameraId
TableName: cam.CameraVolumes, COLUMN_NAME: CameraVolumeId
TableName: cam.CamEvents, COLUMN_NAME: CamEventId
TableName: cam.Cones, COLUMN_NAME: ConeId
TableName: cam.EdgeSettings, COLUMN_NAME: EdgeSettingId
TableName: cam.IpSettings, COLUMN_NAME: IpSettingId
TableName: cam.MediaStreams, COLUMN_NAME: MediaStreamId
TableName: cam.MotionZones, COLUMN_NAME: MotionZoneId
TableName: cam.Presets, COLUMN_NAME: PresetId
TableName: cam.ProCamps, COLUMN_NAME: ProCampId
TableName: cam.PtzSettings, COLUMN_NAME: PtzSettingId
TableName: cam.RecordingSettings, COLUMN_NAME: RecordingSettingId
TableName: cam.RetentionPolicies, COLUMN_NAME: RetentionPolicyId
TableName: cam.StreamProcessings, COLUMN_NAME: StreamProcessingId
TableName: cam.StretchSettings, COLUMN_NAME: StretchSettingId
TableName: cam.VideoOverlays, COLUMN_NAME: VideoOverlayId
TableName: cfg.RegionMembers, COLUMN_NAME: RegionMemberId
TableName: cfg.RegionsHierarchy, COLUMN_NAME: RegionHierarchyId
TableName: cfg.RegionTypes, COLUMN_NAME: RegionTypeId
TableName: cld.VpnProfilePush, COLUMN_NAME: VpnProfilePushId
TableName: com.ActiveDirectories, COLUMN_NAME: ActiveDirectoryId
TableName: com.Certificates, COLUMN_NAME: CertificateId
TableName: com.Connections, COLUMN_NAME: ConnectionId
TableName: com.EmailServers, COLUMN_NAME: EmailServerId
TableName: com.LocalSecureWebServers, COLUMN_NAME: LocalSecureWebServerId
TableName: com.LocalWebServers, COLUMN_NAME: LocalWebServerId
TableName: com.Ports, COLUMN_NAME: PortId
TableName: cso.BandwidthControls, COLUMN_NAME: BandwidthControlId
TableName: dbo.Migrations, COLUMN_NAME: MigrationId
TableName: grp.GroupMembers, COLUMN_NAME: GroupMemberId
TableName: grp.Groups, COLUMN_NAME: GroupId
TableName: lic.Licenses, COLUMN_NAME: LicenseId
TableName: ms.BackupConfigs, COLUMN_NAME: BackupConfigId
TableName: ms.DicoveredServers, COLUMN_NAME: DicoveredServerId
TableName: ms.FederatedChildren, COLUMN_NAME: Guid
TableName: ms.FederatedParent, COLUMN_NAME: Guid
TableName: ms.FederatedSiblings, COLUMN_NAME: Guid
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: ImportRecordingServerQueueId
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: ImportRecordingServerStatusId
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ImportServerConfigQueueGuid
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: ImportServerConfigStatusGuid
TableName: ms.NamedResources, COLUMN_NAME: NamedResourceId
TableName: ms.PushServerStatus, COLUMN_NAME: PushServerStatusId
TableName: ms.PushToServers, COLUMN_NAME: PushToServerId
TableName: ms.VersionInfo, COLUMN_NAME: VersionInfoId
TableName: rs.DefaultBehaviors, COLUMN_NAME: DefaultBehaviorId
TableName: rs.EventNotifications, COLUMN_NAME: EventNotificationId
TableName: rs.EventNotificationSettings, COLUMN_NAME: EventNotificationSettingId
TableName: rs.EventsToLog, COLUMN_NAME: EventToLogId
TableName: rs.EventTriggerActions, COLUMN_NAME: EventTriggerActionId
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerResponseActionId
TableName: rs.EventTriggerResponses, COLUMN_NAME: EventTriggerResponseId
TableName: rs.EventTriggers, COLUMN_NAME: EventTriggerId
TableName: rs.EventTriggerSources, COLUMN_NAME: EventTriggerSourceId
TableName: rs.IoInputs, COLUMN_NAME: IoInputId
TableName: rs.IoOutputCameras, COLUMN_NAME: IoOutputCameraId
TableName: rs.IoOutputs, COLUMN_NAME: IoOutputId
TableName: rs.IOTriggers, COLUMN_NAME: IOTriggerId
TableName: rs.Loggers, COLUMN_NAME: LoggerId
TableName: rs.LogSettings, COLUMN_NAME: LogSettingId
TableName: rs.MediaEncryptions, COLUMN_NAME: ServerGuid
TableName: rs.NVRCameras, COLUMN_NAME: NVRCameraId
TableName: rs.NVRs, COLUMN_NAME: NVRId
TableName: rs.PresetZones, COLUMN_NAME: PresetZoneId
TableName: rs.RecordingServers, COLUMN_NAME: RecordingServerId
TableName: rs.UdpBroadcasts, COLUMN_NAME: UdpBroadcastId
TableName: rs.VideoDecoders, COLUMN_NAME: VideoDecoderId
TableName: rs.VideoEncoders, COLUMN_NAME: VideoEncoderId
TableName: rs.VolumeExpirations, COLUMN_NAME: VolumeExpirationId
TableName: rs.VolumeExports, COLUMN_NAME: VolumeExportId
TableName: rs.VolumeRetentions, COLUMN_NAME: VolumeRetentionId
TableName: rs.Volumes, COLUMN_NAME: VolumeId
TableName: rsd.AudioDevices, COLUMN_NAME: AudioDeviceId
TableName: rsd.AudioDeviceServers, COLUMN_NAME: AudioDeviceServerId
TableName: rsd.CameraEvents, COLUMN_NAME: CameraEventId
TableName: rsd.DeviceTriggers, COLUMN_NAME: DeviceTriggerId
TableName: rsd.DiskDevices, COLUMN_NAME: DiskDeviceId
TableName: rsd.DiskDeviceServers, COLUMN_NAME: DiskDeviceServerId
TableName: rsd.IODevices, COLUMN_NAME: IODeviceId
TableName: rsd.IODeviceServers, COLUMN_NAME: IODeviceServerId
TableName: rsd.MediaProfiles, COLUMN_NAME: MediaProfileId
TableName: rsd.SerialDevices, COLUMN_NAME: SerialDeviceId
TableName: rsd.SerialDeviceServers, COLUMN_NAME: SerialDeviceServerId
TableName: rsd.VideoDevices, COLUMN_NAME: VideoDeviceId
TableName: rsd.VideoDeviceServers, COLUMN_NAME: VideoDeviceServerId
TableName: rsf.PrimariesStandbyServers, COLUMN_NAME: PrimaryStandbyServerId
TableName: rsf.PrimaryRecordingServerFailoverConfigs, COLUMN_NAME: PrimaryRecordingServerFailoverConfigId
TableName: rsf.StandbyRecordingServerFailoverConfigs, COLUMN_NAME: StandbyRecordingServerFailoverConfigId
TableName: rsi.AgentVis, COLUMN_NAME: AgentViId
TableName: rsi.AxisOneClicks, COLUMN_NAME: AxisOneClickId
TableName: rsi.Bolds, COLUMN_NAME: BoldId
TableName: rsi.BriefCams, COLUMN_NAME: BriefCamId
TableName: rsi.S2s, COLUMN_NAME: S2Id
TableName: rsi.SureViews, COLUMN_NAME: SureViewId
TableName: rsp.StorageDrives, COLUMN_NAME: DrivePath
TableName: rsp.StorageDrives, COLUMN_NAME: StoragePoolGuid
TableName: rsp.StoragePools, COLUMN_NAME: StoragePoolGuid
TableName: rsp.StoragePoolSettings, COLUMN_NAME: RecordingServerId
TableName: shl.CameraSchedules, COLUMN_NAME: CameraScheduleId
TableName: shl.EventTriggerSchedules, COLUMN_NAME: EventTriggerScheduleId
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: ScheduledTimeSegmentId
TableName: shl.ScheduleOperations, COLUMN_NAME: ScheduleOperationId
TableName: shl.Schedules, COLUMN_NAME: ScheduleId
TableName: usr.Configurations, COLUMN_NAME: ConfigurationId
TableName: usr.PasswordHistory, COLUMN_NAME: PasswordHistoryId
TableName: usr.PasswordPolicies, COLUMN_NAME: PasswordPolicyId
TableName: usr.Roles, COLUMN_NAME: RoleId
TableName: usr.UserCameras, COLUMN_NAME: UserCameraId
TableName: usr.UserEvents, COLUMN_NAME: UserEventId
TableName: usr.UserEventTriggers, COLUMN_NAME: UserEventTriggerId
TableName: usr.UserInputs, COLUMN_NAME: UserInputId
TableName: usr.UserModules, COLUMN_NAME: UserModuleId
TableName: usr.UserOutputs, COLUMN_NAME: UserOutputId
TableName: usr.UserPasswordPolicies, COLUMN_NAME: UserId
TableName: usr.UserPresets, COLUMN_NAME: UserPresetId
TableName: usr.UserRoles, COLUMN_NAME: UserRoleId
TableName: usr.Users, COLUMN_NAME: UserId
TableName: usr.UserServers, COLUMN_NAME: UserServerId
TableName: usr.UserViews, COLUMN_NAME: UserViewId
TableName: vw.CameraViews, COLUMN_NAME: CameraViewId
TableName: vw.CameraViewTriggers, COLUMN_NAME: CameraViewTriggerId
TableName: vw.FixedTiles, COLUMN_NAME: FixedTileId
TableName: vw.MapImages, COLUMN_NAME: MapImageId
TableName: vw.Maps, COLUMN_NAME: MapId
TableName: vw.MapViewCameras, COLUMN_NAME: MapViewCameraId
TableName: vw.MapViewLinks, COLUMN_NAME: MapViewLinkId
TableName: vw.MapViews, COLUMN_NAME: MapViewId
TableName: vw.Pins, COLUMN_NAME: PinId
TableName: vw.ResponsiveTiles, COLUMN_NAME: ResponsiveTileId
TableName: vw.Tiles, COLUMN_NAME: TileId
TableName: vw.Views, COLUMN_NAME: ViewId
TableName: vw.WallViews, COLUMN_NAME: WallViewId
TableName: vw.WebViews, COLUMN_NAME: WebViewId
TableName: vwt.TemplateTiles, COLUMN_NAME: TemplateTileId
TableName: vwt.ViewTemplates, COLUMN_NAME: ViewTemplateId
TableName: wall.WallViewAgents, COLUMN_NAME: WallViewAgentId
TableName: wall.WvAgentDisplays, COLUMN_NAME: WvAgentDisplayId

--- Foreign Keys ---
FK_Table: cam.Cameras, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: cam.Cameras, FK_Column: VideoDeviceId, PK_Table: rsd.VideoDevices, PK_Column: VideoDeviceId
FK_Table: cam.Cameras, FK_Column: AdvancedSettingId, PK_Table: cam.AdvancedSettings, PK_Column: AdvancedSettingId
FK_Table: cam.Cameras, FK_Column: Camera360LensId, PK_Table: cam.Camera360Lens, PK_Column: Camera360LensId
FK_Table: cam.Cameras, FK_Column: ConeId, PK_Table: cam.Cones, PK_Column: ConeId
FK_Table: cam.Cameras, FK_Column: EdgeSettingId, PK_Table: cam.EdgeSettings, PK_Column: EdgeSettingId
FK_Table: cam.Cameras, FK_Column: IpSettingId, PK_Table: cam.IpSettings, PK_Column: IpSettingId
FK_Table: cam.Cameras, FK_Column: PtzSettingId, PK_Table: cam.PtzSettings, PK_Column: PtzSettingId
FK_Table: cam.Cameras, FK_Column: RecordingSettingId, PK_Table: cam.RecordingSettings, PK_Column: RecordingSettingId
FK_Table: cam.Cameras, FK_Column: StreamProcessingId, PK_Table: cam.StreamProcessings, PK_Column: StreamProcessingId
FK_Table: cam.Cameras, FK_Column: StretchSettingId, PK_Table: cam.StretchSettings, PK_Column: StretchSettingId
FK_Table: cam.CameraVolumes, FK_Column: VolumeId, PK_Table: rs.Volumes, PK_Column: VolumeId
FK_Table: cam.CameraVolumes, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: cam.CamEvents, FK_Column: CameraEventId, PK_Table: rsd.CameraEvents, PK_Column: CameraEventId
FK_Table: cam.CamEvents, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: cam.MediaStreams, FK_Column: AudioDeviceId, PK_Table: rsd.AudioDevices, PK_Column: AudioDeviceId
FK_Table: cam.MediaStreams, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: cam.MotionZones, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: cam.Presets, FK_Column: PtzSettingId, PK_Table: cam.PtzSettings, PK_Column: PtzSettingId
FK_Table: cam.ProCamps, FK_Column: MediaStreamId, PK_Table: cam.MediaStreams, PK_Column: MediaStreamId
FK_Table: cam.RetentionPolicies, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: cam.VideoOverlays, FK_Column: StreamProcessingId, PK_Table: cam.StreamProcessings, PK_Column: StreamProcessingId
FK_Table: cfg.RegionMembers, FK_Column: RegionHierarchyId, PK_Table: cfg.RegionsHierarchy, PK_Column: RegionHierarchyId
FK_Table: cfg.RegionMembers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: cfg.RegionMembers, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: cfg.RegionsHierarchy, FK_Column: ParentId, PK_Table: cfg.RegionsHierarchy, PK_Column: RegionHierarchyId
FK_Table: cfg.RegionsHierarchy, FK_Column: RegionTypeId, PK_Table: cfg.RegionTypes, PK_Column: RegionTypeId
FK_Table: com.Certificates, FK_Column: LocalSecureWebServerId, PK_Table: com.LocalSecureWebServers, PK_Column: LocalSecureWebServerId
FK_Table: cso.BandwidthControls, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: grp.GroupMembers, FK_Column: GroupId, PK_Table: grp.Groups, PK_Column: GroupId
FK_Table: grp.GroupMembers, FK_Column: UserId, PK_Table: usr.Users, PK_Column: UserId
FK_Table: grp.Groups, FK_Column: ActiveDirectoryId, PK_Table: com.ActiveDirectories, PK_Column: ActiveDirectoryId
FK_Table: grp.Groups, FK_Column: ParentId, PK_Table: grp.Groups, PK_Column: GroupId
FK_Table: grp.Groups, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: ms.PushServerStatus, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: ms.PushToServers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.DefaultBehaviors, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.EventNotificationSettings, FK_Column: EventNotificationId, PK_Table: rs.EventNotifications, PK_Column: EventNotificationId
FK_Table: rs.EventsToLog, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.EventTriggerActions, FK_Column: EventTriggerId, PK_Table: rs.EventTriggers, PK_Column: EventTriggerId
FK_Table: rs.EventTriggerActions, FK_Column: IOTriggerId, PK_Table: rs.IOTriggers, PK_Column: IOTriggerId
FK_Table: rs.EventTriggerActions, FK_Column: VolumeId, PK_Table: rs.Volumes, PK_Column: VolumeId
FK_Table: rs.EventTriggerActions, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: rs.EventTriggerResponseActions, FK_Column: EventTriggerResponseId, PK_Table: rs.EventTriggerResponses, PK_Column: EventTriggerResponseId
FK_Table: rs.EventTriggerResponseActions, FK_Column: EventTriggerId, PK_Table: rs.EventTriggers, PK_Column: EventTriggerId
FK_Table: rs.EventTriggerResponseActions, FK_Column: EventTriggerActionId, PK_Table: rs.EventTriggerActions, PK_Column: EventTriggerActionId
FK_Table: rs.EventTriggers, FK_Column: EventTriggerResponseId, PK_Table: rs.EventTriggerResponses, PK_Column: EventTriggerResponseId
FK_Table: rs.EventTriggers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.EventTriggerSources, FK_Column: IOTriggerId, PK_Table: rs.IOTriggers, PK_Column: IOTriggerId
FK_Table: rs.EventTriggerSources, FK_Column: EventTriggerId, PK_Table: rs.EventTriggers, PK_Column: EventTriggerId
FK_Table: rs.EventTriggerSources, FK_Column: VolumeId, PK_Table: rs.Volumes, PK_Column: VolumeId
FK_Table: rs.EventTriggerSources, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: rs.EventTriggerSources, FK_Column: UserId, PK_Table: usr.Users, PK_Column: UserId
FK_Table: rs.IoInputs, FK_Column: IOTriggerId, PK_Table: rs.IOTriggers, PK_Column: IOTriggerId
FK_Table: rs.IoOutputCameras, FK_Column: IoTriggerId, PK_Table: rs.IOTriggers, PK_Column: IOTriggerId
FK_Table: rs.IoOutputCameras, FK_Column: IoOutputId, PK_Table: rs.IoOutputs, PK_Column: IoOutputId
FK_Table: rs.IoOutputCameras, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: rs.IoOutputs, FK_Column: IOTriggerId, PK_Table: rs.IOTriggers, PK_Column: IOTriggerId
FK_Table: rs.IOTriggers, FK_Column: IODeviceId, PK_Table: rsd.IODevices, PK_Column: IODeviceId
FK_Table: rs.IOTriggers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.Loggers, FK_Column: LogSettingId, PK_Table: rs.LogSettings, PK_Column: LogSettingId
FK_Table: rs.NVRCameras, FK_Column: NVRId, PK_Table: rs.NVRs, PK_Column: NVRId
FK_Table: rs.NVRCameras, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: rs.NVRs, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.NVRs, FK_Column: VideoDeviceId, PK_Table: rsd.VideoDevices, PK_Column: VideoDeviceId
FK_Table: rs.PresetZones, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.RecordingServers, FK_Column: ConnectionId, PK_Table: com.Connections, PK_Column: ConnectionId
FK_Table: rs.RecordingServers, FK_Column: LocalSecureWebServerId, PK_Table: com.LocalSecureWebServers, PK_Column: LocalSecureWebServerId
FK_Table: rs.RecordingServers, FK_Column: LocalWebServerId, PK_Table: com.LocalWebServers, PK_Column: LocalWebServerId
FK_Table: rs.RecordingServers, FK_Column: PortId, PK_Table: com.Ports, PK_Column: PortId
FK_Table: rs.RecordingServers, FK_Column: EventNotificationId, PK_Table: rs.EventNotifications, PK_Column: EventNotificationId
FK_Table: rs.RecordingServers, FK_Column: LicenseId, PK_Table: lic.Licenses, PK_Column: LicenseId
FK_Table: rs.RecordingServers, FK_Column: LogSettingId, PK_Table: rs.LogSettings, PK_Column: LogSettingId
FK_Table: rs.RecordingServers, FK_Column: UdpBroadcastId, PK_Table: rs.UdpBroadcasts, PK_Column: UdpBroadcastId
FK_Table: rs.RecordingServers, FK_Column: PrimaryRecordingServerFailoverConfigId, PK_Table: rsf.PrimaryRecordingServerFailoverConfigs, PK_Column: PrimaryRecordingServerFailoverConfigId
FK_Table: rs.RecordingServers, FK_Column: StandbyRecordingServerFailoverConfigId, PK_Table: rsf.StandbyRecordingServerFailoverConfigs, PK_Column: StandbyRecordingServerFailoverConfigId
FK_Table: rs.VideoDecoders, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.VideoEncoders, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rs.VolumeExpirations, FK_Column: VolumeId, PK_Table: rs.Volumes, PK_Column: VolumeId
FK_Table: rs.VolumeExports, FK_Column: VolumeId, PK_Table: rs.Volumes, PK_Column: VolumeId
FK_Table: rs.VolumeRetentions, FK_Column: ArchiveVolumeId, PK_Table: rs.Volumes, PK_Column: VolumeId
FK_Table: rs.VolumeRetentions, FK_Column: BackupVolumeId, PK_Table: rs.Volumes, PK_Column: VolumeId
FK_Table: rs.VolumeRetentions, FK_Column: VolumeId, PK_Table: rs.Volumes, PK_Column: VolumeId
FK_Table: rs.Volumes, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsd.AudioDeviceServers, FK_Column: AudioDeviceId, PK_Table: rsd.AudioDevices, PK_Column: AudioDeviceId
FK_Table: rsd.AudioDeviceServers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsd.CameraEvents, FK_Column: VideoDeviceId, PK_Table: rsd.VideoDevices, PK_Column: VideoDeviceId
FK_Table: rsd.DeviceTriggers, FK_Column: VideoDeviceId, PK_Table: rsd.VideoDevices, PK_Column: VideoDeviceId
FK_Table: rsd.DiskDeviceServers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsd.DiskDeviceServers, FK_Column: DiskDeviceId, PK_Table: rsd.DiskDevices, PK_Column: DiskDeviceId
FK_Table: rsd.IODeviceServers, FK_Column: IODeviceId, PK_Table: rsd.IODevices, PK_Column: IODeviceId
FK_Table: rsd.IODeviceServers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsd.MediaProfiles, FK_Column: VideoDeviceId, PK_Table: rsd.VideoDevices, PK_Column: VideoDeviceId
FK_Table: rsd.SerialDeviceServers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsd.SerialDeviceServers, FK_Column: SerialDeviceId, PK_Table: rsd.SerialDevices, PK_Column: SerialDeviceId
FK_Table: rsd.VideoDevices, FK_Column: ProviderServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsd.VideoDeviceServers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsd.VideoDeviceServers, FK_Column: VideoDeviceId, PK_Table: rsd.VideoDevices, PK_Column: VideoDeviceId
FK_Table: rsf.PrimariesStandbyServers, FK_Column: PrimaryRecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsf.PrimariesStandbyServers, FK_Column: StandbyRecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsf.StandbyRecordingServerFailoverConfigs, FK_Column: PrimaryRecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsi.AgentVis, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsi.AxisOneClicks, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsi.Bolds, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsi.BriefCams, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsi.S2s, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsi.SureViews, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsp.StorageDrives, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsp.StoragePools, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: rsp.StoragePoolSettings, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: shl.CameraSchedules, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: shl.CameraSchedules, FK_Column: ScheduleId, PK_Table: shl.Schedules, PK_Column: ScheduleId
FK_Table: shl.EventTriggerSchedules, FK_Column: EventTriggerId, PK_Table: rs.EventTriggers, PK_Column: EventTriggerId
FK_Table: shl.EventTriggerSchedules, FK_Column: ScheduleId, PK_Table: shl.Schedules, PK_Column: ScheduleId
FK_Table: shl.ScheduledTimeSegments, FK_Column: ScheduleId, PK_Table: shl.Schedules, PK_Column: ScheduleId
FK_Table: shl.ScheduledTimeSegments, FK_Column: ScheduleOperationId, PK_Table: shl.ScheduleOperations, PK_Column: ScheduleOperationId
FK_Table: shl.ScheduleOperations, FK_Column: ScheduleId, PK_Table: shl.Schedules, PK_Column: ScheduleId
FK_Table: shl.Schedules, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: usr.PasswordHistory, FK_Column: UserId, PK_Table: usr.Users, PK_Column: UserId
FK_Table: usr.UserCameras, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: usr.UserCameras, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserEvents, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserEventTriggers, FK_Column: EventTriggerId, PK_Table: rs.EventTriggers, PK_Column: EventTriggerId
FK_Table: usr.UserEventTriggers, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserInputs, FK_Column: IOInputId, PK_Table: rs.IoInputs, PK_Column: IoInputId
FK_Table: usr.UserInputs, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserModules, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserOutputs, FK_Column: IOOutputId, PK_Table: rs.IoOutputs, PK_Column: IoOutputId
FK_Table: usr.UserOutputs, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserPasswordPolicies, FK_Column: UserId, PK_Table: usr.Users, PK_Column: UserId
FK_Table: usr.UserPresets, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: usr.UserPresets, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserRoles, FK_Column: RoleId, PK_Table: usr.Roles, PK_Column: RoleId
FK_Table: usr.UserRoles, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.Users, FK_Column: ActiveDirectoryId, PK_Table: com.ActiveDirectories, PK_Column: ActiveDirectoryId
FK_Table: usr.Users, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserServers, FK_Column: RecordingServerId, PK_Table: rs.RecordingServers, PK_Column: RecordingServerId
FK_Table: usr.UserServers, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserViews, FK_Column: ConfigurationId, PK_Table: usr.Configurations, PK_Column: ConfigurationId
FK_Table: usr.UserViews, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.CameraViews, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: vw.CameraViews, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.CameraViewTriggers, FK_Column: IoOutputId, PK_Table: rs.IoOutputs, PK_Column: IoOutputId
FK_Table: vw.CameraViewTriggers, FK_Column: CameraViewId, PK_Table: vw.CameraViews, PK_Column: CameraViewId
FK_Table: vw.CameraViewTriggers, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.FixedTiles, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.FixedTiles, FK_Column: TileId, PK_Table: vw.Tiles, PK_Column: TileId
FK_Table: vw.Maps, FK_Column: MapImageId, PK_Table: vw.MapImages, PK_Column: MapImageId
FK_Table: vw.MapViewCameras, FK_Column: CameraId, PK_Table: cam.Cameras, PK_Column: CameraId
FK_Table: vw.MapViewCameras, FK_Column: MapViewId, PK_Table: vw.MapViews, PK_Column: MapViewId
FK_Table: vw.MapViewCameras, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.MapViewLinks, FK_Column: MapViewId, PK_Table: vw.MapViews, PK_Column: MapViewId
FK_Table: vw.MapViewLinks, FK_Column: LinkedViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.MapViewLinks, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.MapViews, FK_Column: MapId, PK_Table: vw.Maps, PK_Column: MapId
FK_Table: vw.MapViews, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.Pins, FK_Column: MapViewId, PK_Table: vw.MapViews, PK_Column: MapViewId
FK_Table: vw.Pins, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.ResponsiveTiles, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.ResponsiveTiles, FK_Column: TileId, PK_Table: vw.Tiles, PK_Column: TileId
FK_Table: vw.ResponsiveTiles, FK_Column: ParentTileId, PK_Table: vw.ResponsiveTiles, PK_Column: ResponsiveTileId
FK_Table: vw.Tiles, FK_Column: CameraViewId, PK_Table: vw.CameraViews, PK_Column: CameraViewId
FK_Table: vw.Tiles, FK_Column: MapViewId, PK_Table: vw.MapViews, PK_Column: MapViewId
FK_Table: vw.Tiles, FK_Column: WallViewId, PK_Table: vw.WallViews, PK_Column: WallViewId
FK_Table: vw.Tiles, FK_Column: WebViewId, PK_Table: vw.WebViews, PK_Column: WebViewId
FK_Table: vw.Views, FK_Column: CreatedByUserId, PK_Table: usr.Users, PK_Column: UserId
FK_Table: vw.Views, FK_Column: RelatedViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.WallViews, FK_Column: WvAgentDisplayId, PK_Table: wall.WvAgentDisplays, PK_Column: WvAgentDisplayId
FK_Table: vw.WallViews, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vw.WebViews, FK_Column: ViewId, PK_Table: vw.Views, PK_Column: ViewId
FK_Table: vwt.TemplateTiles, FK_Column: ParentTemplateTileId, PK_Table: vwt.TemplateTiles, PK_Column: TemplateTileId
FK_Table: vwt.TemplateTiles, FK_Column: ViewTemplateId, PK_Table: vwt.ViewTemplates, PK_Column: ViewTemplateId
FK_Table: wall.WvAgentDisplays, FK_Column: WallViewAgentId, PK_Table: wall.WallViewAgents, PK_Column: WallViewAgentId

--- Indexes ---
TableName: rsi.S2s, IndexName: IX_S2s_RecordingServerId, ColumnName: RecordingServerId
TableName: rsi.SureViews, IndexName: IX_SureViews_RecordingServerId, ColumnName: RecordingServerId
TableName: cam.Cameras, IndexName: IX_Camera_Guid, ColumnName: CameraGuid
TableName: rsi.AgentVis, IndexName: IX_AgentVis_RecordingServerId, ColumnName: RecordingServerId
TableName: rsi.BriefCams, IndexName: IX_BriefCams_RecordingServerId, ColumnName: RecordingServerId
TableName: ms.FederatedChildren, IndexName: IX_FederatedChildren_HostAddress_Port, ColumnName: HostAddress
TableName: ms.FederatedChildren, IndexName: IX_FederatedChildren_HostAddress_Port, ColumnName: Port
TableName: ms.FederatedSiblings, IndexName: IX_FederatedSiblings_HostAddress_Port, ColumnName: HostAddress
TableName: ms.FederatedSiblings, IndexName: IX_FederatedSiblings_HostAddress_Port, ColumnName: Port
TableName: rs.EventTriggers, IndexName: IX_EventTrigger_Guid, ColumnName: EventTriggerGuid
TableName: cfg.RegionTypes, IndexName: IX_RegionTypes_Name, ColumnName: Name
TableName: cfg.RegionsHierarchy, IndexName: IX_RegionsHierarchy_ParentId_RegionTypeId_Name, ColumnName: ParentId
TableName: cfg.RegionsHierarchy, IndexName: IX_RegionsHierarchy_ParentId_RegionTypeId_Name, ColumnName: Name
TableName: cfg.RegionsHierarchy, IndexName: IX_RegionsHierarchy_RegionGuid, ColumnName: RegionGuid
TableName: cfg.RegionMembers, IndexName: IX_RegionMembers_ViewId, ColumnName: RegionHierarchyId
TableName: cfg.RegionMembers, IndexName: IX_RegionMembers_ViewId, ColumnName: ViewId
TableName: rsd.IODevices, IndexName: IX_IODevices_DeviceGuid, ColumnName: DeviceGuid
TableName: tmp.TmpInts, IndexName: IX_TmpInts_Id, ColumnName: Id
TableName: tmp.TmpInts, IndexName: IX_TmpInts_Id, ColumnName: IntValue
TableName: cam.RetentionPolicies, IndexName: ID_RetentionPolicies_CameraId_PoolType, ColumnName: CameraId
TableName: cam.RetentionPolicies, IndexName: ID_RetentionPolicies_CameraId_PoolType, ColumnName: PoolType
TableName: tmp.TmpGuids, IndexName: IX_TmpGuids_Guid, ColumnName: Guid
TableName: tmp.TmpGuids, IndexName: IX_TmpGuids_Guid, ColumnName: Value
TableName: tmp.TmpStrings, IndexName: IX_TmpStrings_Guid, ColumnName: Guid
TableName: tmp.TmpStrings, IndexName: IX_TmpStrings_Guid, ColumnName: Value
TableName: rsd.AudioDevices, IndexName: IX_AudioDevices_DeviceGuid, ColumnName: DeviceGuid
TableName: cs.CommonSettings, IndexName: IX_CommonSettings_CommonSettingId, ColumnName: CommonSettingId
TableName: cs.CommonSettings, IndexName: IX_CommonSettings_CommonSettingId, ColumnName: Settings
TableName: cso.BandwidthControls, IndexName: IX_BandwidthControls_BandwidthControlId, ColumnName: BandwidthControlId
TableName: wall.WallViewAgents, IndexName: IX_WallViewAgents_AgentGuid, ColumnName: AgentGuid
TableName: cs.LoggingComponents, IndexName: IX_LoggingComponents_LoggingComponentType, ColumnName: LoggingComponentType
TableName: ms.ImportRecordingServerQueue, IndexName: IX_ImportRecordingServerQueue_HostIp_PortNo, ColumnName: HostAddress
TableName: ms.ImportRecordingServerQueue, IndexName: IX_ImportRecordingServerQueue_HostIp_PortNo, ColumnName: SvrCtrlPortNo
TableName: rsd.DiskDevices, IndexName: IX_DiskDevices_DeviceGuid, ColumnName: DeviceGuid
TableName: ms.ImportRecordingServerStatus, IndexName: IX_ImportRecordingServerStatus_HostIp_PortNo, ColumnName: HostAddress
TableName: ms.ImportRecordingServerStatus, IndexName: IX_ImportRecordingServerStatus_HostIp_PortNo, ColumnName: SvrCtrlPortNo
TableName: vw.Maps, IndexName: IX_Map_Guid, ColumnName: MapGuid
TableName: rsd.VideoDevices, IndexName: IX_VideoDevices_DeviceGuid, ColumnName: DeviceGuid
TableName: com.EmailServers, IndexName: IX_EmailServers_Domain, ColumnName: SmtpHostAddress
TableName: com.ActiveDirectories, IndexName: IX_ActiveDirectories_Domain, ColumnName: Domain
TableName: ms.DicoveredServers, IndexName: IX_SDicoveredServers_ServerGuid, ColumnName: ServerGuid
TableName: ms.NamedResources, IndexName: IX_NamedResources_Name, ColumnName: Name
TableName: rsd.MediaProfiles, IndexName: IX_MediaProfiles_VideoDeviceId, ColumnName: VideoDeviceId
TableName: rsd.SerialDevices, IndexName: IX_SerialDevices_DeviceGuid, ColumnName: DeviceGuid
TableName: shl.Schedules, IndexName: IX_Schedules_ScheduleGuid, ColumnName: ScheduleGuid
TableName: rs.Volumes, IndexName: IX_Volume_Guid, ColumnName: VolumeGuid
TableName: rs.Volumes, IndexName: ID_Volumes_RecordingServerId_Path, ColumnName: RecordingServerId
TableName: rs.Volumes, IndexName: ID_Volumes_RecordingServerId_Path, ColumnName: Path
TableName: rsp.StoragePools, IndexName: ID_StoragePools_RecordingServerId_PoolType, ColumnName: RecordingServerId
TableName: rsp.StoragePools, IndexName: ID_StoragePools_RecordingServerId_PoolType, ColumnName: PoolType
TableName: rsp.StorageDrives, IndexName: ID_StorageDrives_RecordingServerId_DrivePath, ColumnName: RecordingServerId
TableName: rsp.StorageDrives, IndexName: ID_StorageDrives_RecordingServerId_DrivePath, ColumnName: DrivePath
TableName: vw.Views, IndexName: IX_View_Guid, ColumnName: ViewGuid
TableName: rs.IOTriggers, IndexName: IX_IOTrigger_Guid, ColumnName: IOTriggerGuid
TableName: rs.RecordingServers, IndexName: IX_RecordingServers_RecordingServerGuid, ColumnName: RecordingServerGuid
TableName: rs.NVRs, IndexName: NVRs_NVRGuid, ColumnName: NVRGuid
TableName: usr.Users, IndexName: IX_Users_Username, ColumnName: Username
TableName: usr.Users, IndexName: IX_Users_UserGuid, ColumnName: UserGuid
TableName: usr.Users, IndexName: IX_Users_Email, ColumnName: Email
TableName: grp.Groups, IndexName: IX_Groups_ParentId_Name, ColumnName: ParentId
TableName: grp.Groups, IndexName: IX_Groups_ParentId_Name, ColumnName: Name
TableName: grp.GroupMembers, IndexName: IX_Groups_GroupId, ColumnName: GroupId
TableName: grp.GroupMembers, IndexName: IX_Groups_GroupId, ColumnName: UserId
TableName: rsi.AxisOneClicks, IndexName: IX_AxisOneClicks_RecordingServerId, ColumnName: RecordingServerId
TableName: rsi.Bolds, IndexName: IX_Bolds_RecordingServerId, ColumnName: RecordingServerId

--- Row Counts ---
TableName: ms.PushServerStatus, RowCounts: 1686
TableName: vwt.TemplateTiles, RowCounts: 398
TableName: usr.UserEvents, RowCounts: 327
TableName: dbo.Migrations, RowCounts: 236
TableName: rsd.DeviceTriggers, RowCounts: 165
TableName: rs.EventsToLog, RowCounts: 146
TableName: shl.ScheduledTimeSegments, RowCounts: 105
TableName: usr.UserModules, RowCounts: 80
TableName: cam.Presets, RowCounts: 70
TableName: rs.Loggers, RowCounts: 63
TableName: rsd.CameraEvents, RowCounts: 59
TableName: cam.ProCamps, RowCounts: 35
TableName: usr.UserCameras, RowCounts: 34
TableName: rs.EventNotificationSettings, RowCounts: 30
TableName: usr.UserViews, RowCounts: 29
TableName: vwt.ViewTemplates, RowCounts: 25
TableName: shl.ScheduleOperations, RowCounts: 25
TableName: rsd.VideoDevices, RowCounts: 24
TableName: rsd.VideoDeviceServers, RowCounts: 24
TableName: cam.VideoOverlays, RowCounts: 21
TableName: cs.LoggingComponents, RowCounts: 21
TableName: rs.PresetZones, RowCounts: 20
TableName: vw.ResponsiveTiles, RowCounts: 19
TableName: vw.Tiles, RowCounts: 19
TableName: vw.CameraViews, RowCounts: 17
TableName: usr.Configurations, RowCounts: 14
TableName: usr.UserServers, RowCounts: 12
TableName: cam.RetentionPolicies, RowCounts: 12
TableName: vw.Views, RowCounts: 9
TableName: rs.UdpBroadcasts, RowCounts: 9
TableName: usr.Users, RowCounts: 8
TableName: usr.Roles, RowCounts: 8
TableName: cfg.RegionTypes, RowCounts: 8
TableName: grp.GroupMembers, RowCounts: 8
TableName: cam.EdgeSettings, RowCounts: 7
TableName: cam.IpSettings, RowCounts: 7
TableName: rsd.MediaProfiles, RowCounts: 7
TableName: cam.MediaStreams, RowCounts: 7
TableName: cam.Cones, RowCounts: 7
TableName: cam.Cameras, RowCounts: 7
TableName: cam.Camera360Lens, RowCounts: 7
TableName: cam.RecordingSettings, RowCounts: 7
TableName: cam.PtzSettings, RowCounts: 7
TableName: cam.StreamProcessings, RowCounts: 7
TableName: cam.StretchSettings, RowCounts: 7
TableName: grp.Groups, RowCounts: 6
TableName: rsd.DiskDevices, RowCounts: 5
TableName: rsd.DiskDeviceServers, RowCounts: 5
TableName: vw.MapViewCameras, RowCounts: 5
TableName: usr.UserRoles, RowCounts: 5
TableName: rs.VideoDecoders, RowCounts: 4
TableName: shl.Schedules, RowCounts: 4
TableName: shl.CameraSchedules, RowCounts: 4
TableName: rsd.AudioDevices, RowCounts: 3
TableName: rsd.AudioDeviceServers, RowCounts: 3
TableName: com.Connections, RowCounts: 3
TableName: cs.CommonSettings, RowCounts: 3
TableName: rs.EventNotifications, RowCounts: 3
TableName: lic.Licenses, RowCounts: 3
TableName: com.LocalSecureWebServers, RowCounts: 3
TableName: com.LocalWebServers, RowCounts: 3
TableName: rs.LogSettings, RowCounts: 3
TableName: ms.ImportRecordingServerStatus, RowCounts: 3
TableName: usr.PasswordHistory, RowCounts: 3
TableName: com.Ports, RowCounts: 3
TableName: cam.MotionZones, RowCounts: 3
TableName: rs.RecordingServers, RowCounts: 2
TableName: rsi.S2s, RowCounts: 2
TableName: ms.VersionInfo, RowCounts: 2
TableName: rs.VideoEncoders, RowCounts: 2
TableName: rsi.SureViews, RowCounts: 2
TableName: rs.DefaultBehaviors, RowCounts: 2
TableName: rsi.AxisOneClicks, RowCounts: 2
TableName: rsi.Bolds, RowCounts: 2
TableName: rsi.BriefCams, RowCounts: 2
TableName: rsi.AgentVis, RowCounts: 2
TableName: cam.CamEvents, RowCounts: 2
TableName: com.Certificates, RowCounts: 1
TableName: cam.CameraVolumes, RowCounts: 1
TableName: cld.Credentials, RowCounts: 1
TableName: ms.Identity, RowCounts: 1
TableName: vw.MapImages, RowCounts: 1
TableName: vw.Maps, RowCounts: 1
TableName: vw.MapViews, RowCounts: 1
TableName: rs.Volumes, RowCounts: 1
TableName: wall.WallViewAgents, RowCounts: 1
TableName: vw.WallViews, RowCounts: 1
TableName: cfg.RegionsHierarchy, RowCounts: 1
TableName: cld.Service, RowCounts: 1
TableName: rsp.StorageDrives, RowCounts: 1
TableName: rsp.StoragePools, RowCounts: 1
TableName: rsp.StoragePoolSettings, RowCounts: 1
TableName: usr.PasswordPolicies, RowCounts: 1
TableName: wall.WvAgentDisplays, RowCounts: 1
TableName: vw.Pins, RowCounts: 0
TableName: ms.PushToServers, RowCounts: 0
TableName: ms.NamedResources, RowCounts: 0
TableName: rs.NVRCameras, RowCounts: 0
TableName: rs.NVRs, RowCounts: 0
TableName: rsf.PrimariesStandbyServers, RowCounts: 0
TableName: rsf.PrimaryRecordingServerFailoverConfigs, RowCounts: 0
TableName: rsf.StandbyRecordingServerFailoverConfigs, RowCounts: 0
TableName: rsd.SerialDevices, RowCounts: 0
TableName: rsd.SerialDeviceServers, RowCounts: 0
TableName: cfg.RegionMembers, RowCounts: 0
TableName: vw.WebViews, RowCounts: 0
TableName: cld.VpnProfilePush, RowCounts: 0
TableName: rs.VolumeExpirations, RowCounts: 0
TableName: rs.VolumeExports, RowCounts: 0
TableName: rs.VolumeRetentions, RowCounts: 0
TableName: tmp.TmpGuids, RowCounts: 0
TableName: tmp.TmpInts, RowCounts: 0
TableName: tmp.TmpStrings, RowCounts: 0
TableName: usr.UserEventTriggers, RowCounts: 0
TableName: usr.UserInputs, RowCounts: 0
TableName: usr.UserOutputs, RowCounts: 0
TableName: usr.UserPasswordPolicies, RowCounts: 0
TableName: usr.UserPresets, RowCounts: 0
TableName: rs.MediaEncryptions, RowCounts: 0
TableName: vw.MapViewLinks, RowCounts: 0
TableName: ms.ImportRecordingServerQueue, RowCounts: 0
TableName: com.EmailServers, RowCounts: 0
TableName: ms.ImportServerConfigQueue, RowCounts: 0
TableName: ms.ImportServerConfigStatus, RowCounts: 0
TableName: rsd.IODevices, RowCounts: 0
TableName: rsd.IODeviceServers, RowCounts: 0
TableName: rs.IoInputs, RowCounts: 0
TableName: rs.IoOutputCameras, RowCounts: 0
TableName: rs.IoOutputs, RowCounts: 0
TableName: rs.IOTriggers, RowCounts: 0
TableName: ms.DicoveredServers, RowCounts: 0
TableName: rs.EventTriggerActions, RowCounts: 0
TableName: rs.EventTriggerResponseActions, RowCounts: 0
TableName: rs.EventTriggerResponses, RowCounts: 0
TableName: rs.EventTriggers, RowCounts: 0
TableName: shl.EventTriggerSchedules, RowCounts: 0
TableName: rs.EventTriggerSources, RowCounts: 0
TableName: ms.FederatedChildren, RowCounts: 0
TableName: ms.FederatedParent, RowCounts: 0
TableName: ms.FederatedSiblings, RowCounts: 0
TableName: ms.FederationSettings, RowCounts: 0
TableName: vw.FixedTiles, RowCounts: 0
TableName: vw.CameraViewTriggers, RowCounts: 0
TableName: com.ActiveDirectories, RowCounts: 0
TableName: cam.AdvancedSettings, RowCounts: 0
TableName: ms.BackupConfigs, RowCounts: 0
TableName: cso.BandwidthControls, RowCounts: 0

--- Unique Constraints ---
TableName: rsi.S2s, ConstraintName: IX_S2s_RecordingServerId, ColumnName: RecordingServerId
TableName: rsi.SureViews, ConstraintName: IX_SureViews_RecordingServerId, ColumnName: RecordingServerId
TableName: cam.Cameras, ConstraintName: IX_Camera_Guid, ColumnName: CameraGuid
TableName: rsi.AgentVis, ConstraintName: IX_AgentVis_RecordingServerId, ColumnName: RecordingServerId
TableName: rsi.BriefCams, ConstraintName: IX_BriefCams_RecordingServerId, ColumnName: RecordingServerId
TableName: ms.FederatedChildren, ConstraintName: IX_FederatedChildren_HostAddress_Port, ColumnName: HostAddress
TableName: ms.FederatedChildren, ConstraintName: IX_FederatedChildren_HostAddress_Port, ColumnName: Port
TableName: ms.FederatedSiblings, ConstraintName: IX_FederatedSiblings_HostAddress_Port, ColumnName: HostAddress
TableName: ms.FederatedSiblings, ConstraintName: IX_FederatedSiblings_HostAddress_Port, ColumnName: Port
TableName: rs.EventTriggers, ConstraintName: IX_EventTrigger_Guid, ColumnName: EventTriggerGuid
TableName: cfg.RegionTypes, ConstraintName: IX_RegionTypes_Name, ColumnName: Name
TableName: cfg.RegionsHierarchy, ConstraintName: IX_RegionsHierarchy_ParentId_RegionTypeId_Name, ColumnName: ParentId
TableName: cfg.RegionsHierarchy, ConstraintName: IX_RegionsHierarchy_ParentId_RegionTypeId_Name, ColumnName: Name
TableName: cfg.RegionsHierarchy, ConstraintName: IX_RegionsHierarchy_RegionGuid, ColumnName: RegionGuid
TableName: cfg.RegionMembers, ConstraintName: IX_RegionMembers_ViewId, ColumnName: RegionHierarchyId
TableName: cfg.RegionMembers, ConstraintName: IX_RegionMembers_ViewId, ColumnName: ViewId
TableName: rsd.IODevices, ConstraintName: IX_IODevices_DeviceGuid, ColumnName: DeviceGuid
TableName: cam.RetentionPolicies, ConstraintName: ID_RetentionPolicies_CameraId_PoolType, ColumnName: CameraId
TableName: cam.RetentionPolicies, ConstraintName: ID_RetentionPolicies_CameraId_PoolType, ColumnName: PoolType
TableName: rsd.AudioDevices, ConstraintName: IX_AudioDevices_DeviceGuid, ColumnName: DeviceGuid
TableName: cs.CommonSettings, ConstraintName: IX_CommonSettings_CommonSettingId, ColumnName: CommonSettingId
TableName: cs.CommonSettings, ConstraintName: IX_CommonSettings_CommonSettingId, ColumnName: Settings
TableName: cso.BandwidthControls, ConstraintName: IX_BandwidthControls_BandwidthControlId, ColumnName: BandwidthControlId
TableName: wall.WallViewAgents, ConstraintName: IX_WallViewAgents_AgentGuid, ColumnName: AgentGuid
TableName: cs.LoggingComponents, ConstraintName: IX_LoggingComponents_LoggingComponentType, ColumnName: LoggingComponentType
TableName: ms.ImportRecordingServerQueue, ConstraintName: IX_ImportRecordingServerQueue_HostIp_PortNo, ColumnName: HostAddress
TableName: ms.ImportRecordingServerQueue, ConstraintName: IX_ImportRecordingServerQueue_HostIp_PortNo, ColumnName: SvrCtrlPortNo
TableName: rsd.DiskDevices, ConstraintName: IX_DiskDevices_DeviceGuid, ColumnName: DeviceGuid
TableName: ms.ImportRecordingServerStatus, ConstraintName: IX_ImportRecordingServerStatus_HostIp_PortNo, ColumnName: HostAddress
TableName: ms.ImportRecordingServerStatus, ConstraintName: IX_ImportRecordingServerStatus_HostIp_PortNo, ColumnName: SvrCtrlPortNo
TableName: vw.Maps, ConstraintName: IX_Map_Guid, ColumnName: MapGuid
TableName: rsd.VideoDevices, ConstraintName: IX_VideoDevices_DeviceGuid, ColumnName: DeviceGuid
TableName: com.EmailServers, ConstraintName: IX_EmailServers_Domain, ColumnName: SmtpHostAddress
TableName: com.ActiveDirectories, ConstraintName: IX_ActiveDirectories_Domain, ColumnName: Domain
TableName: ms.DicoveredServers, ConstraintName: IX_SDicoveredServers_ServerGuid, ColumnName: ServerGuid
TableName: ms.NamedResources, ConstraintName: IX_NamedResources_Name, ColumnName: Name
TableName: rsd.SerialDevices, ConstraintName: IX_SerialDevices_DeviceGuid, ColumnName: DeviceGuid
TableName: shl.Schedules, ConstraintName: IX_Schedules_ScheduleGuid, ColumnName: ScheduleGuid
TableName: rs.Volumes, ConstraintName: IX_Volume_Guid, ColumnName: VolumeGuid
TableName: rs.Volumes, ConstraintName: ID_Volumes_RecordingServerId_Path, ColumnName: RecordingServerId
TableName: rs.Volumes, ConstraintName: ID_Volumes_RecordingServerId_Path, ColumnName: Path
TableName: rsp.StoragePools, ConstraintName: ID_StoragePools_RecordingServerId_PoolType, ColumnName: RecordingServerId
TableName: rsp.StoragePools, ConstraintName: ID_StoragePools_RecordingServerId_PoolType, ColumnName: PoolType
TableName: rsp.StorageDrives, ConstraintName: ID_StorageDrives_RecordingServerId_DrivePath, ColumnName: RecordingServerId
TableName: rsp.StorageDrives, ConstraintName: ID_StorageDrives_RecordingServerId_DrivePath, ColumnName: DrivePath
TableName: vw.Views, ConstraintName: IX_View_Guid, ColumnName: ViewGuid
TableName: rs.IOTriggers, ConstraintName: IX_IOTrigger_Guid, ColumnName: IOTriggerGuid
TableName: rs.RecordingServers, ConstraintName: IX_RecordingServers_RecordingServerGuid, ColumnName: RecordingServerGuid
TableName: rs.NVRs, ConstraintName: NVRs_NVRGuid, ColumnName: NVRGuid
TableName: usr.Users, ConstraintName: IX_Users_Username, ColumnName: Username
TableName: usr.Users, ConstraintName: IX_Users_UserGuid, ColumnName: UserGuid
TableName: usr.Users, ConstraintName: IX_Users_Email, ColumnName: Email
TableName: grp.Groups, ConstraintName: IX_Groups_ParentId_Name, ColumnName: ParentId
TableName: grp.Groups, ConstraintName: IX_Groups_ParentId_Name, ColumnName: Name
TableName: grp.GroupMembers, ConstraintName: IX_Groups_GroupId, ColumnName: GroupId
TableName: grp.GroupMembers, ConstraintName: IX_Groups_GroupId, ColumnName: UserId
TableName: rsi.AxisOneClicks, ConstraintName: IX_AxisOneClicks_RecordingServerId, ColumnName: RecordingServerId
TableName: rsi.Bolds, ConstraintName: IX_Bolds_RecordingServerId, ColumnName: RecordingServerId

--- Nullability and Defaults ---
TableName: cam.AdvancedSettings, COLUMN_NAME: AdvancedSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.AdvancedSettings, COLUMN_NAME: VideoEncoderType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.AdvancedSettings, COLUMN_NAME: VideoDecoderType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Camera360Lens, COLUMN_NAME: Camera360LensId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Camera360Lens, COLUMN_NAME: DewarpLensType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Camera360Lens, COLUMN_NAME: DewarpLensOrientationType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Camera360Lens, COLUMN_NAME: DewarpImmervisionLensProfileType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Camera360Lens, COLUMN_NAME: DewarpViewType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Camera360Lens, COLUMN_NAME: FishEyeCenterX, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Camera360Lens, COLUMN_NAME: FishEyeCenterY, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Camera360Lens, COLUMN_NAME: FishEyeRadius, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: CameraGuid, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: VideoDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: TimeZone, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: CameraNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: ChannelNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: Latitude, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: Longitude, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: AlarmPresetType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: MotionSensitivity, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: AdvancedSettingId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: EdgeSettingId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: RecordingSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: Camera360LensId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: IpSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: StretchSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: PtzSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: StreamProcessingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: ConeId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Cameras, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.CameraVolumes, COLUMN_NAME: CameraVolumeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.CameraVolumes, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.CameraVolumes, COLUMN_NAME: VolumeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.CamEvents, COLUMN_NAME: CamEventId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.CamEvents, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.CamEvents, COLUMN_NAME: CameraEventId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.CamEvents, COLUMN_NAME: OnEventType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.CamEvents, COLUMN_NAME: OffEventType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.CamEvents, COLUMN_NAME: EventAlias, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.CamEvents, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: ((1))
TableName: cam.Cones, COLUMN_NAME: ConeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cones, COLUMN_NAME: RotateAngle, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cones, COLUMN_NAME: WidthAngle, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Cones, COLUMN_NAME: Depth, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: EdgeSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: EdgeStorageType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: WillFormatSdCard, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: NasAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: NasPath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: NasUserName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: NasPassword, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: SyncStartTime, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: SyncInterval, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.EdgeSettings, COLUMN_NAME: WillSyncServerOffline, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: IpSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: IpAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: UserName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: Manufacturer, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: Model, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: ProtocolType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: RttpPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: ControlPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: StreamPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: RtspTcpPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: RtspUdpPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: HttpPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: Path, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: Timeout, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: Retries, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: UseAxisProxy, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: KeepAliveMethodType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: UseAxisStreamProfile, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.IpSettings, COLUMN_NAME: AxisStreamProfile, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: MediaStreamId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: StreamType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: StreamName, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: StreamTokenName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: StreamNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: MultiStreamNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoSourceFormatType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoSourceFormatProfileType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoWidth, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoHeight, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoResolution, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoQuality, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoFps, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoGov, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoBitrateControlType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoAverageBitrate, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoMaximumBitrate, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: IsAudioEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: AudioDeviceId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: AudioChannelNo, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoRecordFormatType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: VideoLiveFormatType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: IsScheduledRecordingEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: IsAlarmRecordingEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: IsMotionRecordingEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: IsOnDemandCamera, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: OnDemandDisconnectTime, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MediaStreams, COLUMN_NAME: RecompressionOn, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: MotionZoneId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: ZoneIndex, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: PointX, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: PointY, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: Width, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: Height, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: Priority, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.MotionZones, COLUMN_NAME: DoRecSchedule, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Presets, COLUMN_NAME: PresetId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Presets, COLUMN_NAME: PtzSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Presets, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.Presets, COLUMN_NAME: PresetIndex, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Presets, COLUMN_NAME: DwellTimeSeconds, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.Presets, COLUMN_NAME: DoIncludeTour, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.ProCamps, COLUMN_NAME: ProCampId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.ProCamps, COLUMN_NAME: MediaStreamId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.ProCamps, COLUMN_NAME: PropertyType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.ProCamps, COLUMN_NAME: Value, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.ProCamps, COLUMN_NAME: Flags, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: PtzSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: IsPtzEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: ChannelNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: PtzDriver, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: SerialPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: DoFlipDirections, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: TourResumeTime, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: InactivityTimeoutInterval, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: DoAutoHome, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: DoPresetTour, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: DoTourOffOnStart, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: DoPersistConnection, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: UseAnalogPtz, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: HomePreset1, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: HomePreset2, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: HomePreset3, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.PtzSettings, COLUMN_NAME: HomePreset4, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: RecordingSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: StorageType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: SchedFps, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: MotionFps, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: AlarmFps, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: PreMotionSeconds, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: PostMotionSeconds, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: PreAlarmSecords, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RecordingSettings, COLUMN_NAME: PostAlarmSeconds, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: RetentionPolicyId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: PoolType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: ActionAfterDays, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: MinRetentionDays, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: MaxRetentionDays, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: StartTime, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: ContinuousClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: MotionClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.RetentionPolicies, COLUMN_NAME: AlarmClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: StreamProcessingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: ExternalCameraNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: IsStreamProcessingEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: IsDftEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: IsDvdEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: WillFavorContinuousVideo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: DoVideoAnalytics, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: LatencyDft, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StreamProcessings, COLUMN_NAME: GopCountDft, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StretchSettings, COLUMN_NAME: StretchSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StretchSettings, COLUMN_NAME: IsDeinterlacingEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StretchSettings, COLUMN_NAME: IsMotionBasedDeinterlacingEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StretchSettings, COLUMN_NAME: MotionDeinterlacingThreshold, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StretchSettings, COLUMN_NAME: IsMedianFilteringEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StretchSettings, COLUMN_NAME: IsNoiseReductionEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StretchSettings, COLUMN_NAME: NoiseReductionLumaStrength, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.StretchSettings, COLUMN_NAME: NoiseReductionChromaStrength, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.VideoOverlays, COLUMN_NAME: VideoOverlayId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.VideoOverlays, COLUMN_NAME: StreamProcessingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.VideoOverlays, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.VideoOverlays, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cam.VideoOverlays, COLUMN_NAME: VideoOverlayType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cam.VideoOverlays, COLUMN_NAME: VideoOverlayPositionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionMembers, COLUMN_NAME: RegionMemberId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionMembers, COLUMN_NAME: RegionHierarchyId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionMembers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cfg.RegionMembers, COLUMN_NAME: ViewId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cfg.RegionsHierarchy, COLUMN_NAME: RegionHierarchyId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionsHierarchy, COLUMN_NAME: RegionGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionsHierarchy, COLUMN_NAME: ParentId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cfg.RegionsHierarchy, COLUMN_NAME: RegionTypeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionsHierarchy, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionTypes, COLUMN_NAME: RegionTypeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionTypes, COLUMN_NAME: Ordinal, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cfg.RegionTypes, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cld.Credentials, COLUMN_NAME: Email, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cld.Credentials, COLUMN_NAME: Password, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cld.Credentials, COLUMN_NAME: Secret, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cld.Service, COLUMN_NAME: HostAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cld.Service, COLUMN_NAME: Port, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cld.Service, COLUMN_NAME: IsSecure, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cld.Service, COLUMN_NAME: DefaultTimeout, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: cld.VpnProfilePush, COLUMN_NAME: VpnProfilePushId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cld.VpnProfilePush, COLUMN_NAME: ServerGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cld.VpnProfilePush, COLUMN_NAME: VpnAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: ActiveDirectoryId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: Domain, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: BaseDn, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: Username, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: SearchNestedDomains, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: SearchNestedGroups, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.ActiveDirectories, COLUMN_NAME: GroupReauthIntervalSecs, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Certificates, COLUMN_NAME: CertificateId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Certificates, COLUMN_NAME: LocalSecureWebServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Certificates, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Connections, COLUMN_NAME: ConnectionId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Connections, COLUMN_NAME: HostAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.Connections, COLUMN_NAME: Username, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.Connections, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.Connections, COLUMN_NAME: Driver, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.Connections, COLUMN_NAME: Retries, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: EmailServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: SmtpHostAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: SmtpHostPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: Username, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: WillUseTls, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: WillAuthenticate, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: SenderName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.EmailServers, COLUMN_NAME: SenderEmail, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.LocalSecureWebServers, COLUMN_NAME: LocalSecureWebServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.LocalSecureWebServers, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.LocalSecureWebServers, COLUMN_NAME: Port, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.LocalSecureWebServers, COLUMN_NAME: MinTlsVersion, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.LocalWebServers, COLUMN_NAME: LocalWebServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.LocalWebServers, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.LocalWebServers, COLUMN_NAME: Port, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: PortId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: DataPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: SecureDataPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: AdminPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: UdpMediaPortMin, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: UdpMediaPortMax, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: RtspPortIsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: RtspPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: com.Ports, COLUMN_NAME: RtspUseBasicAuth, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: cs.CommonSettings, COLUMN_NAME: CommonSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cs.CommonSettings, COLUMN_NAME: Settings, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cs.LoggingComponents, COLUMN_NAME: LoggingComponentType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cs.LoggingComponents, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cs.LoggingComponents, COLUMN_NAME: Identifier, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cs.LoggingComponents, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cso.BandwidthControls, COLUMN_NAME: BandwidthControlId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cso.BandwidthControls, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cso.BandwidthControls, COLUMN_NAME: IsOnDemandEventsChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: cso.BandwidthControls, COLUMN_NAME: StatusPeriodMs, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: dbo.Migrations, COLUMN_NAME: MigrationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: dbo.Migrations, COLUMN_NAME: SQL, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: dbo.Migrations, COLUMN_NAME: FileName, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: dbo.Migrations, COLUMN_NAME: AppliedDate, IS_NULLABLE: NO, COLUMN_DEFAULT: (getdate())
TableName: dbo.Migrations, COLUMN_NAME: SqlHash, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.GroupMembers, COLUMN_NAME: GroupMemberId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.GroupMembers, COLUMN_NAME: GroupId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.GroupMembers, COLUMN_NAME: UserId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: GroupId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: ParentId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: ActiveDirectoryId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: SubDomain, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: GroupGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: IsAdmin, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: RemoteAccess, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: PtzPriority, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: grp.Groups, COLUMN_NAME: MsOwnerGuid, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: lic.Licenses, COLUMN_NAME: LicenseId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: lic.Licenses, COLUMN_NAME: LicenseType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: lic.Licenses, COLUMN_NAME: IpLicensesPurchased, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: lic.Licenses, COLUMN_NAME: AnalogLicensesPurchased, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: lic.Licenses, COLUMN_NAME: FeatureKeysJson, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.BackupConfigs, COLUMN_NAME: BackupConfigId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.BackupConfigs, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.BackupConfigs, COLUMN_NAME: IsDefault, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.BackupConfigs, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.BackupConfigs, COLUMN_NAME: CronExpression, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.BackupConfigs, COLUMN_NAME: BackupPath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.BackupConfigs, COLUMN_NAME: BackupTimestampFormat, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.BackupConfigs, COLUMN_NAME: BackupOptions, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.DicoveredServers, COLUMN_NAME: DicoveredServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.DicoveredServers, COLUMN_NAME: IpAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.DicoveredServers, COLUMN_NAME: FriendlyName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.DicoveredServers, COLUMN_NAME: ServerGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.DicoveredServers, COLUMN_NAME: LastUpdated, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: Guid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: Version, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: UseTls, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: HostAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: Port, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: Identity, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: Password, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: WriteToken, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedChildren, COLUMN_NAME: RecordingServersCount, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: ms.FederatedChildren, COLUMN_NAME: CameraLicensesPurchased, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: ms.FederatedChildren, COLUMN_NAME: CameraLicensesUsed, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: ms.FederatedParent, COLUMN_NAME: Guid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedParent, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedParent, COLUMN_NAME: Version, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.FederatedParent, COLUMN_NAME: UseTls, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedParent, COLUMN_NAME: HostAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedParent, COLUMN_NAME: Port, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedParent, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.FederatedParent, COLUMN_NAME: WriteToken, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedSiblings, COLUMN_NAME: Guid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedSiblings, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedSiblings, COLUMN_NAME: Version, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.FederatedSiblings, COLUMN_NAME: UseTls, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedSiblings, COLUMN_NAME: HostAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederatedSiblings, COLUMN_NAME: Port, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.FederationSettings, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.Identity, COLUMN_NAME: Guid, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.Identity, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.Identity, COLUMN_NAME: HostAddressesJson, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.Identity, COLUMN_NAME: BaseUrl, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: ImportRecordingServerQueueId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: FriendlyName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: HostAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: SvrCtrlPortNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: RestPortNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: UseTls, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: Username, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: Password, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: AutoProvision, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: ConnectionTimeoutMs, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: AddUserId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: EnqueuedAt, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: RegionHierarchyId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerQueue, COLUMN_NAME: PostActions, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: ImportRecordingServerStatusId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: FriendlyName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: HostAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: SvrCtrlPortNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: RestPortNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: UseTls, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: Username, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: Password, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: AutoProvision, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: AddUserId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: RegionHierarchyId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: EnqueuedAt, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: ImportStatus, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: StartedAt, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: EndedAt, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: Elapsed, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: ErrorMessage, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportRecordingServerStatus, COLUMN_NAME: PostActions, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ImportServerConfigQueueGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: AutoProvision, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: FriendlyName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: HostAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ServerGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: Version, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: UseTls, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: Username, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: Platform, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: SerialNo, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: EnqueuedAt, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ServerTimeZoneInfo, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: EnvironmentInfo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: ServerConfigRoot, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigQueue, COLUMN_NAME: LicenseInfo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: ImportServerConfigStatusGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: HostAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: ServerGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: StartedAt, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: EndedAt, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: Elapsed, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: WasSuccessful, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.ImportServerConfigStatus, COLUMN_NAME: Message, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.NamedResources, COLUMN_NAME: NamedResourceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.NamedResources, COLUMN_NAME: ResourceType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.NamedResources, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.NamedResources, COLUMN_NAME: Description, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.NamedResources, COLUMN_NAME: HostAddress, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.NamedResources, COLUMN_NAME: SecondaryAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: PushServerStatusId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: PushToServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: PushDevices, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: PushUsers, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: PushConfiguration, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: CorsWhitelist, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: TimeStamp, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: WasSuccessful, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: ErrorMessage, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.PushServerStatus, COLUMN_NAME: PushMediaEnc, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: ms.PushToServers, COLUMN_NAME: PushToServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: PushDevices, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: PushUsers, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: PushConfiguration, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: CorsWhitelist, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: TimeStamp, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: HttpPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: SecureHttpPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.PushToServers, COLUMN_NAME: Priority, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: ms.PushToServers, COLUMN_NAME: PushMediaEnc, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: ms.VersionInfo, COLUMN_NAME: VersionInfoId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.VersionInfo, COLUMN_NAME: Major, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.VersionInfo, COLUMN_NAME: Minor, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: ms.VersionInfo, COLUMN_NAME: Build, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.VersionInfo, COLUMN_NAME: Revision, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: ms.VersionInfo, COLUMN_NAME: TimeStamp, IS_NULLABLE: YES, COLUMN_DEFAULT: (NULL)
TableName: rs.DefaultBehaviors, COLUMN_NAME: DefaultBehaviorId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.DefaultBehaviors, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.DefaultBehaviors, COLUMN_NAME: PacketLossMaxAcceptPercent, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.DefaultBehaviors, COLUMN_NAME: PacketLossReportIntervalSec, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.DefaultBehaviors, COLUMN_NAME: IsCameraAccessEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotifications, COLUMN_NAME: EventNotificationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotifications, COLUMN_NAME: EmailSubject, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventNotifications, COLUMN_NAME: EmailRecipients, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventNotifications, COLUMN_NAME: SmsSubject, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventNotifications, COLUMN_NAME: SmsRecipients, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventNotifications, COLUMN_NAME: SmsGateway, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: EventNotificationSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: EventNotificationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: SourceType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: EventType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: WillIncludeSnapshot, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: MinInterval, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: ConfirmationWaitFor, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventNotificationSettings, COLUMN_NAME: ConfirmationWaitEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventsToLog, COLUMN_NAME: EventToLogId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventsToLog, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventsToLog, COLUMN_NAME: EventType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: EventTriggerActionId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: EventTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: EventTriggerActionGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: ActionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: ActionValue, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: PriorityType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: IsPrimaryAction, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: CameraId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: IOTriggerId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerActions, COLUMN_NAME: VolumeId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerResponseActionId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerResponseId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponseActions, COLUMN_NAME: EventTriggerActionId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponses, COLUMN_NAME: EventTriggerResponseId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponses, COLUMN_NAME: EventTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponses, COLUMN_NAME: IsAckRequired, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponses, COLUMN_NAME: IsResetRequired, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerResponses, COLUMN_NAME: CanForward, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: EventTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: EventTriggerResponseId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: EventTriggerGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: SetIOTrigger, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: PriorityType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggers, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: EventTriggerSourceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: EventTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: EventSourceGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: EventType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: EventValue, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: IsPrimaryEvent, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: CameraId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: IOTriggerId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: VolumeId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.EventTriggerSources, COLUMN_NAME: UserId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.IoInputs, COLUMN_NAME: IoInputId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoInputs, COLUMN_NAME: IOTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoInputs, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoInputs, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.IoInputs, COLUMN_NAME: PinNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoInputs, COLUMN_NAME: TriggerStateType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoInputs, COLUMN_NAME: EventType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputCameras, COLUMN_NAME: IoOutputCameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputCameras, COLUMN_NAME: IoTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputCameras, COLUMN_NAME: IoOutputId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputCameras, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: IoOutputId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: IOTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: PinNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: TriggerStateType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: ResetTimeMs, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: DeviceType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IoOutputs, COLUMN_NAME: WillAutoReset, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: IOTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: IODeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: IOTriggerGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: Username, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: DriverName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: IpAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.IOTriggers, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.Loggers, COLUMN_NAME: LoggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Loggers, COLUMN_NAME: LogSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Loggers, COLUMN_NAME: ComponentNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Loggers, COLUMN_NAME: PriorityNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Loggers, COLUMN_NAME: DoFlushOnWrite, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Loggers, COLUMN_NAME: DoRolloverOnSaveCfg, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Loggers, COLUMN_NAME: KeepForDays, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.LogSettings, COLUMN_NAME: LogSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.LogSettings, COLUMN_NAME: LogFilePath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: ServerGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: Enabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: CipherMode, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: KeySize, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: PasswordBase64, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: Iterations, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: HashAlgorithm, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: KekBase64, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: DekBase64, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: DekCreatedOnUtc, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.MediaEncryptions, COLUMN_NAME: WriteToken, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRCameras, COLUMN_NAME: NVRCameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRCameras, COLUMN_NAME: NVRId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRCameras, COLUMN_NAME: CameraId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRCameras, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRCameras, COLUMN_NAME: ChannelNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRCameras, COLUMN_NAME: IsVmsVolume, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRCameras, COLUMN_NAME: IsNvrStorage, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: NVRId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: VideoDeviceId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: NVRGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: Manufacturer, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: Model, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: ChannelCount, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: TimeZone, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: HttpPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: RtspPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: ControlPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: HostAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: Username, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: TimeoutSecs, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: NumRetries, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.NVRs, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.PresetZones, COLUMN_NAME: PresetZoneId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.PresetZones, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.PresetZones, COLUMN_NAME: PriorityType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.PresetZones, COLUMN_NAME: HoldTimeSeconds, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.PresetZones, COLUMN_NAME: DwellTimeSeconds, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.PresetZones, COLUMN_NAME: IsCyclingEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: RecordingServerGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: Description, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: TimeZone, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: Version, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: ProductId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: UseTls, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: IpLicensesUsed, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: AnalogLicensesUsed, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: IsLegacy, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: Platform, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: SerialNumber, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: WasAutoProvisioned, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: LastSync, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: FeatureKeyVersion, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: Latitude, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: Longitude, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: PortId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: ConnectionId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: LicenseId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: LocalWebServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: LocalSecureWebServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: LogSettingId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: EventNotificationId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: UdpBroadcastId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: PrimaryRecordingServerFailoverConfigId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: StandbyRecordingServerFailoverConfigId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.RecordingServers, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.UdpBroadcasts, COLUMN_NAME: UdpBroadcastId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.UdpBroadcasts, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.UdpBroadcasts, COLUMN_NAME: Port, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.UdpBroadcasts, COLUMN_NAME: RepeatCount, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.UdpBroadcasts, COLUMN_NAME: WaitTimeoutMs, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: VideoDecoderId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: VideoCompressionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: TranscoderEngineType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: VideoCompressionProfileType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: LatencyType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: VideoCompressionRateType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: BitRate, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: GopLength, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoDecoders, COLUMN_NAME: HardwareAccelerationType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: VideoEncoderId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: VideoCompressionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: TranscoderEngineType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: VideoCompressionProfileType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: LatencyType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: VideoCompressionRateType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: BitRate, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: GopLength, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VideoEncoders, COLUMN_NAME: HardwareAccelerationType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: VolumeExpirationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: VolumeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: StartTime, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: Frequency, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: MaxVideoDays, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: IsScheduleClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: IsMotionClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExpirations, COLUMN_NAME: IsAlarmClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExports, COLUMN_NAME: VolumeExportId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExports, COLUMN_NAME: VolumeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExports, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExports, COLUMN_NAME: StartTime, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.VolumeExports, COLUMN_NAME: Frequency, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExports, COLUMN_NAME: WillStoreAllExports, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeExports, COLUMN_NAME: WillStoreServerExportQueue, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: VolumeRetentionId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: VolumeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: ArchiveVolumeId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: BackupVolumeId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: StartTime, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: Frequency, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: RetentionDays, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: IsScheduleClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: IsMotionClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.VolumeRetentions, COLUMN_NAME: IsAlarmClips, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: VolumeId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: VolumeGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: VolumeType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: Path, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: UncPath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: FreeSpacePercentage, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: DesiredMinVideoDays, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rs.Volumes, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: AudioDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: DeviceGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: DriverType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: ConnectionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: InternalNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: DeviceAttributes, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: StatusType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: WasAutoDetected, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: LastSync, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: Manufacturer, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: Model, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: Quality, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: ChannelMode, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: IpV4Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: IpV6Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDevices, COLUMN_NAME: ComPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.AudioDeviceServers, COLUMN_NAME: AudioDeviceServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDeviceServers, COLUMN_NAME: AudioDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.AudioDeviceServers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: CameraEventId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: VideoDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: CameraEventType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: Category, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: EventName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: SubEventName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: OnEventType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: OffEventType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.CameraEvents, COLUMN_NAME: IsPulsed, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DeviceTriggers, COLUMN_NAME: DeviceTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DeviceTriggers, COLUMN_NAME: VideoDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DeviceTriggers, COLUMN_NAME: IsInput, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DeviceTriggers, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.DeviceTriggers, COLUMN_NAME: IdleState, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: DiskDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: DeviceGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: DriverType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: ConnectionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: InternalNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: DeviceAttributes, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: StatusType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: WasAutoDetected, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: LastSync, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: TotalSizeMB, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: FreeSpaceMB, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDevices, COLUMN_NAME: IsIndexingEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDeviceServers, COLUMN_NAME: DiskDeviceServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDeviceServers, COLUMN_NAME: DiskDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.DiskDeviceServers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: IODeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: DeviceGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: DriverType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: ConnectionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: InternalNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: DeviceAttributes, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: StatusType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: WasAutoDetected, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: LastSync, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: ComPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: InputCount, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: OutputCount, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: Manufacturer, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: Model, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: IpV4Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.IODevices, COLUMN_NAME: IpV6Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.IODeviceServers, COLUMN_NAME: IODeviceServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODeviceServers, COLUMN_NAME: IODeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.IODeviceServers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MediaProfileId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: VideoDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: ChannelNo, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: Token, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: SnapshotUrl, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: StreamUrl, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: EncodingProfile, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: VideoEncoderType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxWidth, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxHeight, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: Resolutions, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MinGovLength, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxGovLength, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: GovLength, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MinFps, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxFps, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: Fps, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MinQuality, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxQuality, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: Quality, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MinBitrate, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: MaxBitrate, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: Bitrate, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: IsAudioAvailable, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: AudioEncodingType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: AudioBitrate, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.MediaProfiles, COLUMN_NAME: AudioSampleRate, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: SerialDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: DeviceGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: DriverType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: ConnectionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: InternalNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: DeviceAttributes, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: StatusType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: WasAutoDetected, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: LastSync, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: PortNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: BaudRate, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: DataBits, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: ParityType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: StopBits, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDevices, COLUMN_NAME: FlowControl, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDeviceServers, COLUMN_NAME: SerialDeviceServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDeviceServers, COLUMN_NAME: SerialDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.SerialDeviceServers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: VideoDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: ProviderServerId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: DeviceGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: DriverType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: ConnectionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: InternalNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: DeviceAttributes, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: StatusType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: WasAutoDetected, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: LastSync, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: MacAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: SerialNumber, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: Manufacturer, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: Model, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: IsPtz, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: ChannelCount, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: PresetCount, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: IpV4Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: IpV6Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: HttpPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: HttpsPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: RtspPort, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: Firmware, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: VideoStandardType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: SourceFilePath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: OnvifDeviceUrl, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: OnvifPtzUrl, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: OnvifEventUrl, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: StretchSdkVersion, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: StretchDriverVersion, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: StretchFirmwareVersion, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: StretchBootLoaderVersion, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: StretchBspVersion, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: StretchPciSlotNumber, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: StretchCardIsPresent, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: OnBoardEventsLastSync, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDevices, COLUMN_NAME: SupportsAudio, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: rsd.VideoDevices, COLUMN_NAME: VideoCompressionsArr, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsd.VideoDeviceServers, COLUMN_NAME: VideoDeviceServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDeviceServers, COLUMN_NAME: VideoDeviceId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsd.VideoDeviceServers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsf.PrimariesStandbyServers, COLUMN_NAME: PrimaryStandbyServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsf.PrimariesStandbyServers, COLUMN_NAME: PrimaryRecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsf.PrimariesStandbyServers, COLUMN_NAME: StandbyRecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsf.PrimaryRecordingServerFailoverConfigs, COLUMN_NAME: PrimaryRecordingServerFailoverConfigId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsf.PrimaryRecordingServerFailoverConfigs, COLUMN_NAME: FailoverAfterMS, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsf.PrimaryRecordingServerFailoverConfigs, COLUMN_NAME: RestoreAfterMS, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsf.StandbyRecordingServerFailoverConfigs, COLUMN_NAME: StandbyRecordingServerFailoverConfigId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsf.StandbyRecordingServerFailoverConfigs, COLUMN_NAME: PrimaryRecordingServerId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsf.StandbyRecordingServerFailoverConfigs, COLUMN_NAME: SharePath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.AgentVis, COLUMN_NAME: AgentViId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AgentVis, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AgentVis, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AgentVis, COLUMN_NAME: EngineAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.AgentVis, COLUMN_NAME: ProxyLocalPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: AxisOneClickId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: DispatchServerAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: UserName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: ProxyExternalAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: ProxyExternalPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: ProxyInternalAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: ProxyInternalPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: OcccVersion, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.AxisOneClicks, COLUMN_NAME: CertificatePath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.Bolds, COLUMN_NAME: BoldId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.Bolds, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.Bolds, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.Bolds, COLUMN_NAME: ManitouServerAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.Bolds, COLUMN_NAME: ManitouPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.Bolds, COLUMN_NAME: HeartbeatInterval, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: BriefCamId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: Port, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: UserName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: AdminPath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.BriefCams, COLUMN_NAME: UserPath, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.S2s, COLUMN_NAME: S2Id, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.S2s, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.S2s, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.S2s, COLUMN_NAME: S2ControllerAddress, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.SureViews, COLUMN_NAME: SureViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.SureViews, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.SureViews, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.SureViews, COLUMN_NAME: ImmixDeviceNumber, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.SureViews, COLUMN_NAME: ImmixSmtpServer, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: rsi.SureViews, COLUMN_NAME: ImmixSmtpPort, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.SureViews, COLUMN_NAME: ImmixNumberOfSnapshots, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsi.SureViews, COLUMN_NAME: ImmixDurationSeconds, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StorageDrives, COLUMN_NAME: StoragePoolGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StorageDrives, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StorageDrives, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StorageDrives, COLUMN_NAME: DrivePath, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StorageDrives, COLUMN_NAME: VideoSpacePercent, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StorageDrives, COLUMN_NAME: IsOverflow, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StoragePools, COLUMN_NAME: StoragePoolGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StoragePools, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StoragePools, COLUMN_NAME: PoolType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StoragePools, COLUMN_NAME: WarningLevelPercent, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StoragePoolSettings, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: rsp.StoragePoolSettings, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.CameraSchedules, COLUMN_NAME: CameraScheduleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.CameraSchedules, COLUMN_NAME: ScheduleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.CameraSchedules, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.EventTriggerSchedules, COLUMN_NAME: EventTriggerScheduleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.EventTriggerSchedules, COLUMN_NAME: ScheduleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.EventTriggerSchedules, COLUMN_NAME: EventTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: ScheduledTimeSegmentId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: ScheduleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: ScheduleOperationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: OnDate, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: DayOfWeekType, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: StartTime, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.ScheduledTimeSegments, COLUMN_NAME: EndTime, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.ScheduleOperations, COLUMN_NAME: ScheduleOperationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.ScheduleOperations, COLUMN_NAME: ScheduleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.ScheduleOperations, COLUMN_NAME: OperationType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.Schedules, COLUMN_NAME: ScheduleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.Schedules, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.Schedules, COLUMN_NAME: ScheduleGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.Schedules, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.Schedules, COLUMN_NAME: ResourceType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.Schedules, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.Schedules, COLUMN_NAME: IsDefault, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: shl.Schedules, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: tmp.TmpGuids, COLUMN_NAME: Guid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: tmp.TmpGuids, COLUMN_NAME: Value, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: tmp.TmpInts, COLUMN_NAME: Id, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: tmp.TmpInts, COLUMN_NAME: IntValue, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: tmp.TmpStrings, COLUMN_NAME: Guid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: tmp.TmpStrings, COLUMN_NAME: Value, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsAppAutoLoginChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsAppCustomFavoritesChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsAppServersCamerasChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsAppServerIpAddressChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsAppAutoFullscreenChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsPtzChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsLvAutoSequenceChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsLvVideoPanelChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsLvEventPanelChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsLvMapPanelChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsLvWallPanelChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsMaxTileCountEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: MaxTileCount, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsAvMapPanelChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsAvCameraPanelChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: PlayersType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsDbUserConnectionsChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsDbSearchVideoChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Configurations, COLUMN_NAME: IsDbSearchEventsChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.PasswordHistory, COLUMN_NAME: PasswordHistoryId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.PasswordHistory, COLUMN_NAME: UserId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.PasswordHistory, COLUMN_NAME: SetOn, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.PasswordHistory, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.PasswordPolicies, COLUMN_NAME: PasswordPolicyId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.PasswordPolicies, COLUMN_NAME: PolicyJson, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Roles, COLUMN_NAME: RoleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Roles, COLUMN_NAME: Identifier, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Roles, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: UserCameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: IsLiveChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: IsPlaybackChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: IsAudioChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: IsExportChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: IsSnapshotChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: IsLightChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: IsPtzChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: PlaybackTimeout, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.UserCameras, COLUMN_NAME: IsFavorite, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEvents, COLUMN_NAME: UserEventId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEvents, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEvents, COLUMN_NAME: EventType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEvents, COLUMN_NAME: IsChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEventTriggers, COLUMN_NAME: UserEventTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEventTriggers, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEventTriggers, COLUMN_NAME: EventTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEventTriggers, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEventTriggers, COLUMN_NAME: WillExecute, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserEventTriggers, COLUMN_NAME: IsFavorite, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserInputs, COLUMN_NAME: UserInputId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserInputs, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserInputs, COLUMN_NAME: IOInputId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserInputs, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserInputs, COLUMN_NAME: IsFavorite, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserModules, COLUMN_NAME: UserModuleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserModules, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserModules, COLUMN_NAME: ModuleType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserModules, COLUMN_NAME: HasAccess, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserModules, COLUMN_NAME: IsSetAtStartup, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserOutputs, COLUMN_NAME: UserOutputId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserOutputs, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserOutputs, COLUMN_NAME: IOOutputId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserOutputs, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserOutputs, COLUMN_NAME: WillExecute, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserOutputs, COLUMN_NAME: IsFavorite, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserPasswordPolicies, COLUMN_NAME: UserId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserPasswordPolicies, COLUMN_NAME: PolicyJson, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.UserPresets, COLUMN_NAME: UserPresetId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserPresets, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserPresets, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserPresets, COLUMN_NAME: PresetIndex, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserPresets, COLUMN_NAME: IsSetChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserPresets, COLUMN_NAME: IsShowChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserRoles, COLUMN_NAME: UserRoleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserRoles, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserRoles, COLUMN_NAME: RoleId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: UserId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: UserGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: IsAdmin, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: RemoteAccess, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: ActiveDirectoryId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: SubDomain, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: Username, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: Password, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: IsDefaultPassword, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: PtzPriority, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: IsManaged, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: ExpiresOn, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: LastName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: Email, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: ResetOn, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: FirstName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: Phone, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.Users, COLUMN_NAME: Cloud, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: usr.Users, COLUMN_NAME: MsOwnerGuid, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.UserServers, COLUMN_NAME: UserServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserServers, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserServers, COLUMN_NAME: RecordingServerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserServers, COLUMN_NAME: IsAdminChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserServers, COLUMN_NAME: IsApiChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserServers, COLUMN_NAME: IsEventsChecked, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserServers, COLUMN_NAME: PtzPriorityType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserServers, COLUMN_NAME: IsFavorite, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserViews, COLUMN_NAME: UserViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserViews, COLUMN_NAME: ConfigurationId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserViews, COLUMN_NAME: ViewId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: usr.UserViews, COLUMN_NAME: IsFavorite, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: usr.UserViews, COLUMN_NAME: Navigation, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: usr.UserViews, COLUMN_NAME: ShowUrl, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: usr.UserViews, COLUMN_NAME: LiveView, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: usr.UserViews, COLUMN_NAME: Playback, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: vw.CameraViews, COLUMN_NAME: CameraViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.CameraViews, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.CameraViews, COLUMN_NAME: CameraId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.CameraViews, COLUMN_NAME: FitOption, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.CameraViewTriggers, COLUMN_NAME: CameraViewTriggerId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.CameraViewTriggers, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.CameraViewTriggers, COLUMN_NAME: CameraViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.CameraViewTriggers, COLUMN_NAME: IoOutputId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.FixedTiles, COLUMN_NAME: FixedTileId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.FixedTiles, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.FixedTiles, COLUMN_NAME: TileId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.FixedTiles, COLUMN_NAME: PositionX, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.FixedTiles, COLUMN_NAME: PositionY, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.FixedTiles, COLUMN_NAME: Width, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.FixedTiles, COLUMN_NAME: Height, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapImages, COLUMN_NAME: MapImageId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapImages, COLUMN_NAME: ImageBytes, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.MapImages, COLUMN_NAME: CompressionType, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: vw.MapImages, COLUMN_NAME: FileSize, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: vw.MapImages, COLUMN_NAME: Md5, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.MapImages, COLUMN_NAME: Height, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: vw.MapImages, COLUMN_NAME: Width, IS_NULLABLE: NO, COLUMN_DEFAULT: ((0))
TableName: vw.Maps, COLUMN_NAME: MapId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: MapImageId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: MapGuid, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: MapType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: Address, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: Latitude, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: Longitude, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: CompressionType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: FileSize, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: Md5, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: Height, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Maps, COLUMN_NAME: Width, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewCameras, COLUMN_NAME: MapViewCameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewCameras, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewCameras, COLUMN_NAME: MapViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewCameras, COLUMN_NAME: CameraId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewCameras, COLUMN_NAME: PositionX, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.MapViewCameras, COLUMN_NAME: PositionY, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.MapViewLinks, COLUMN_NAME: MapViewLinkId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewLinks, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewLinks, COLUMN_NAME: MapViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewLinks, COLUMN_NAME: LinkedViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViewLinks, COLUMN_NAME: PositionX, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.MapViewLinks, COLUMN_NAME: PositionY, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.MapViews, COLUMN_NAME: MapViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViews, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.MapViews, COLUMN_NAME: MapId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Pins, COLUMN_NAME: PinId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Pins, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Pins, COLUMN_NAME: MapViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Pins, COLUMN_NAME: Latitude, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Pins, COLUMN_NAME: Longitude, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Pins, COLUMN_NAME: PositionX, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Pins, COLUMN_NAME: PositionY, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: ResponsiveTileId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: TileId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: ParentTileId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: LayoutStyle, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: Ordinal, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: AbsoluteWidth, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: AbsoluteHeight, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: PositionX, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: PositionY, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: Row, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: Column, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: RowSpan, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.ResponsiveTiles, COLUMN_NAME: ColumnSpan, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Tiles, COLUMN_NAME: TileId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Tiles, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Tiles, COLUMN_NAME: CameraViewId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Tiles, COLUMN_NAME: MapViewId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Tiles, COLUMN_NAME: WebViewId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Tiles, COLUMN_NAME: WallViewId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: RelatedViewId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: ViewGuid, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: Name, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: IsTemplate, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: DeclaredType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: LayoutStyle, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: Width, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: Height, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: CreatedByUserId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: AspectRatio, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: IsInTour, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: DwellTimeSeconds, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: Fps, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: Quality, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: ShowCones, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: ShowLabel, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: ScaleToFit, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: ZoomLevel, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: PreviewIcon, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.Views, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.WallViews, COLUMN_NAME: WallViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.WallViews, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.WallViews, COLUMN_NAME: WvAgentDisplayId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.WallViews, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.WallViews, COLUMN_NAME: AspectRatio, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.WallViews, COLUMN_NAME: RowCount, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.WallViews, COLUMN_NAME: ColumnCount, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.WebViews, COLUMN_NAME: WebViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.WebViews, COLUMN_NAME: ViewId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vw.WebViews, COLUMN_NAME: WebUrl, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vw.WebViews, COLUMN_NAME: IsAudioEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: ((1))
TableName: vwt.TemplateTiles, COLUMN_NAME: TemplateTileId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: ViewTemplateId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: ParentTemplateTileId, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: TileType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: PointX, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: PointY, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: Height, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: Width, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: Row, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: Column, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: RowSpan, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.TemplateTiles, COLUMN_NAME: ColumnSpan, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.ViewTemplates, COLUMN_NAME: ViewTemplateId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.ViewTemplates, COLUMN_NAME: ViewTemplateGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.ViewTemplates, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: vwt.ViewTemplates, COLUMN_NAME: ViewType, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.ViewTemplates, COLUMN_NAME: LayoutStyle, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: vwt.ViewTemplates, COLUMN_NAME: ImageBytesBase64, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: wall.WallViewAgents, COLUMN_NAME: WallViewAgentId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: wall.WallViewAgents, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: wall.WallViewAgents, COLUMN_NAME: AgentGuid, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: wall.WallViewAgents, COLUMN_NAME: Name, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: wall.WallViewAgents, COLUMN_NAME: HostIp, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: wall.WallViewAgents, COLUMN_NAME: PortNo, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: wall.WallViewAgents, COLUMN_NAME: Note, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: wall.WvAgentDisplays, COLUMN_NAME: WvAgentDisplayId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: wall.WvAgentDisplays, COLUMN_NAME: WallViewAgentId, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: wall.WvAgentDisplays, COLUMN_NAME: IsEnabled, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: wall.WvAgentDisplays, COLUMN_NAME: IsPrimary, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: wall.WvAgentDisplays, COLUMN_NAME: DisplayNo, IS_NULLABLE: NO, COLUMN_DEFAULT: None
TableName: wall.WvAgentDisplays, COLUMN_NAME: DeviceName, IS_NULLABLE: YES, COLUMN_DEFAULT: None
TableName: wall.WvAgentDisplays, COLUMN_NAME: DisplayName, IS_NULLABLE: YES, COLUMN_DEFAULT: None

--- Triggers ---

--- Trigger Definitions ---

--- Views ---

--- View Definitions ---

--- Stored Procedures ---

--- Stored Procedure Parameters ---

--- Stored Procedure Definitions ---

--- Check Constraints ---

--- Default Constraints ---
TableName: cam.CamEvents, ColumnName: IsEnabled, ConstraintName: D_CAMEVENTS_ISENABLED, DefaultValue: ((1))
TableName: com.Ports, ColumnName: RtspUseBasicAuth, ConstraintName: D_PORTS_RTPSUSEBASICAUTH, DefaultValue: ((0))
TableName: dbo.Migrations, ColumnName: AppliedDate, ConstraintName: DF__Migration__Appli__35BCFE0A, DefaultValue: (getdate())
TableName: ms.FederatedChildren, ColumnName: CameraLicensesPurchased, ConstraintName: DF__Federated__Camer__1940BAED, DefaultValue: ((0))
TableName: ms.FederatedChildren, ColumnName: CameraLicensesUsed, ConstraintName: DF__Federated__Camer__1A34DF26, DefaultValue: ((0))
TableName: ms.FederatedChildren, ColumnName: RecordingServersCount, ConstraintName: DF__Federated__Recor__184C96B4, DefaultValue: ((0))
TableName: ms.PushServerStatus, ColumnName: PushMediaEnc, ConstraintName: DF__PushServe__PushM__1EF99443, DefaultValue: ((0))
TableName: ms.PushToServers, ColumnName: Priority, ConstraintName: DF__PushToSer__Prior__7F80E8EA, DefaultValue: ((0))
TableName: ms.PushToServers, ColumnName: PushMediaEnc, ConstraintName: DF__PushToSer__PushM__1E05700A, DefaultValue: ((0))
TableName: ms.VersionInfo, ColumnName: TimeStamp, ConstraintName: DF__VersionIn__TimeS__00750D23, DefaultValue: (NULL)
TableName: rsd.VideoDevices, ColumnName: SupportsAudio, ConstraintName: DF__VideoDevi__Suppo__5B438874, DefaultValue: ((0))
TableName: usr.Users, ColumnName: Cloud, ConstraintName: DF__Users__Cloud__7E8CC4B1, DefaultValue: ((0))
TableName: usr.UserViews, ColumnName: LiveView, ConstraintName: D_USERVIEWS_LIVEVIEW, DefaultValue: ((0))
TableName: usr.UserViews, ColumnName: Navigation, ConstraintName: D_USERVIEWS_NAVIGATION, DefaultValue: ((0))
TableName: usr.UserViews, ColumnName: Playback, ConstraintName: D_USERVIEWS_PLAYBACK, DefaultValue: ((0))
TableName: usr.UserViews, ColumnName: ShowUrl, ConstraintName: D_USERVIEWS_SHOWURL, DefaultValue: ((0))
TableName: vw.MapImages, ColumnName: CompressionType, ConstraintName: DF__MapImages__Compr__6C6E1476, DefaultValue: ((0))
TableName: vw.MapImages, ColumnName: FileSize, ConstraintName: DF__MapImages__FileS__6D6238AF, DefaultValue: ((0))
TableName: vw.MapImages, ColumnName: Height, ConstraintName: DF__MapImages__Heigh__6E565CE8, DefaultValue: ((0))
TableName: vw.MapImages, ColumnName: Width, ConstraintName: DF__MapImages__Width__6F4A8121, DefaultValue: ((0))
TableName: vw.WebViews, ColumnName: IsAudioEnabled, ConstraintName: D_WEBVIEWS_ENABLEAUDIO, DefaultValue: ((1))

--- Extended Properties ---

--- Object Dependencies ---
ReferencingObject: cfg.RegionMembers, ReferencedObject: cfg.RegionMembers
ReferencingObject: usr.Users, ReferencedObject: usr.Users
