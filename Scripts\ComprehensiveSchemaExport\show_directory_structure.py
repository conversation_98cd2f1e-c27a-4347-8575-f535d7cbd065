#!/usr/bin/env python3
"""
Display the organized directory structure for the Comprehensive Schema Export Tool
Shows where exports and logs are stored
"""

import os
from pathlib import Path
from datetime import datetime

def show_directory_structure():
    """Display the organized directory structure"""
    script_dir = Path(__file__).parent.absolute()
    exports_dir = script_dir / "exports"
    logs_dir = script_dir / "logs"
    
    print("=" * 70)
    print("COMPREHENSIVE SCHEMA EXPORT TOOL - DIRECTORY STRUCTURE")
    print("=" * 70)
    
    print(f"\n📁 Script Root Directory:")
    print(f"   {script_dir}")
    
    print(f"\n📁 Exports Directory:")
    print(f"   {exports_dir}")
    if exports_dir.exists():
        print(f"   Status: ✅ Exists")
        # Show subdirectories (timestamped export folders)
        subdirs = [d for d in exports_dir.iterdir() if d.is_dir()]
        if subdirs:
            print(f"   Export Sessions: {len(subdirs)}")
            for subdir in sorted(subdirs, reverse=True)[:5]:  # Show last 5
                file_count = len(list(subdir.glob("*")))
                print(f"     📂 {subdir.name} ({file_count} files)")
        else:
            print(f"   Export Sessions: None yet")
    else:
        print(f"   Status: ⚠️  Will be created on first export")
    
    print(f"\n📁 Logs Directory:")
    print(f"   {logs_dir}")
    if logs_dir.exists():
        print(f"   Status: ✅ Exists")
        # Show log files
        log_files = list(logs_dir.glob("*.log"))
        if log_files:
            print(f"   Log Files: {len(log_files)}")
            for log_file in sorted(log_files, reverse=True)[:5]:  # Show last 5
                size_kb = log_file.stat().st_size / 1024
                print(f"     📄 {log_file.name} ({size_kb:.1f} KB)")
        else:
            print(f"   Log Files: None yet")
    else:
        print(f"   Status: ⚠️  Will be created on first run")
    
    print(f"\n📋 File Organization:")
    print(f"   • Exports are organized by timestamp: YYYYMMDD_HHMMSS")
    print(f"   • Each export session gets its own subdirectory")
    print(f"   • Logs are daily: schema_export_YYYYMMDD.log")
    print(f"   • Desktop backups (if enabled) go to user's Desktop")
    
    print(f"\n🔧 Configuration Files:")
    config_files = [
        "comprehensive_schema_export.py",
        "config_examples.json",
        "requirements.txt",
        "README.md"
    ]
    
    for config_file in config_files:
        file_path = script_dir / config_file
        if file_path.exists():
            print(f"   ✅ {config_file}")
        else:
            print(f"   ❌ {config_file} (missing)")
    
    print(f"\n🚀 Quick Start:")
    print(f"   1. Run: python comprehensive_schema_export.py")
    print(f"   2. Check exports in: {exports_dir}")
    print(f"   3. Check logs in: {logs_dir}")
    
    print(f"\n💡 Tips:")
    print(f"   • Use PowerShell runner: .\\Run-SchemaExport.ps1")
    print(f"   • Check config examples: config_examples.json")
    print(f"   • Enable desktop backups in configuration")
    
    print("=" * 70)

if __name__ == "__main__":
    show_directory_structure()
