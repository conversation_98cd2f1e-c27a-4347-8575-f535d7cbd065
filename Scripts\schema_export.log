2025-06-30 17:10:17,432 - INFO - Starting Comprehensive Schema Export Tool
2025-06-30 17:10:17,433 - INFO - Starting comprehensive schema export...
2025-06-30 17:10:19,522 - WARNING - TCP connectivity test failed, but attempting connection anyway...
2025-06-30 17:10:19,522 - INFO - Attempting connection with driver: <PERSON>DBC Driver 17 for SQL Server (attempt 1)
2025-06-30 17:10:19,708 - INFO - Executing: Enhanced Tables and Columns
2025-06-30 17:10:20,200 - INFO - Executing: Enhanced Foreign Key Relationships
2025-06-30 17:10:20,282 - INFO - Executing: Enhanced Index Information
2025-06-30 17:10:20,290 - INFO - Executing: Check Constraints
2025-06-30 17:10:20,326 - INFO - Executing: Views and Definitions
2025-06-30 17:10:20,564 - INFO - Executing: Stored Procedures and Functions
2025-06-30 17:10:20,584 - INFO - Executing: Triggers
2025-06-30 17:10:20,629 - INFO - Executing: User Defined Data Types
2025-06-30 17:10:20,639 - INFO - Executing: Extended Properties
2025-06-30 17:10:20,654 - INFO - Executing: Table Statistics
2025-06-30 17:10:20,659 - INFO - Executing: Object Dependencies
2025-06-30 17:10:20,687 - INFO - Executing: Database Information
2025-06-30 17:10:20,717 - INFO - Executing: Table list with row counts
2025-06-30 17:10:20,783 - INFO - Found 147 tables for processing
2025-06-30 17:10:20,783 - INFO - Table size range: 0 to 1,686 rows
2025-06-30 17:10:20,786 - INFO - Limiting to 20 tables for detailed sampling (performance protection)
2025-06-30 17:10:20,786 - INFO - Collecting sample data and column statistics for 20 tables...
2025-06-30 17:10:20,786 - INFO - Processing table com.ActiveDirectories (estimated 0 rows)
2025-06-30 17:10:20,787 - INFO - Executing: Sample data from com.ActiveDirectories
2025-06-30 17:10:20,791 - INFO - Executing: Columns for com.ActiveDirectories
2025-06-30 17:10:20,815 - INFO - Executing: Statistics for com.ActiveDirectories.ActiveDirectoryId
2025-06-30 17:10:20,820 - INFO - Executing: Statistics for com.ActiveDirectories.IsEnabled
2025-06-30 17:10:20,826 - INFO - Executing: Statistics for com.ActiveDirectories.Domain
2025-06-30 17:10:20,834 - INFO - Executing: Distinct values for com.ActiveDirectories.Domain
2025-06-30 17:10:20,842 - INFO - Executing: Pattern analysis for com.ActiveDirectories.Domain
2025-06-30 17:10:20,850 - INFO - Executing: Statistics for com.ActiveDirectories.BaseDn
2025-06-30 17:10:20,860 - INFO - Executing: Distinct values for com.ActiveDirectories.BaseDn
2025-06-30 17:10:20,866 - INFO - Executing: Pattern analysis for com.ActiveDirectories.BaseDn
2025-06-30 17:10:20,874 - INFO - Executing: Statistics for com.ActiveDirectories.Username
2025-06-30 17:10:20,879 - INFO - Executing: Distinct values for com.ActiveDirectories.Username
2025-06-30 17:10:20,885 - INFO - Executing: Pattern analysis for com.ActiveDirectories.Username
2025-06-30 17:10:20,891 - INFO - Executing: Statistics for com.ActiveDirectories.Password
2025-06-30 17:10:20,897 - INFO - Executing: Distinct values for com.ActiveDirectories.Password
2025-06-30 17:10:20,904 - INFO - Executing: Pattern analysis for com.ActiveDirectories.Password
2025-06-30 17:10:20,911 - INFO - Executing: Statistics for com.ActiveDirectories.SearchNestedDomains
2025-06-30 17:10:20,917 - INFO - Executing: Statistics for com.ActiveDirectories.SearchNestedGroups
2025-06-30 17:10:20,924 - INFO - Executing: Statistics for com.ActiveDirectories.GroupReauthIntervalSecs
2025-06-30 17:10:20,929 - INFO - Processing table cam.AdvancedSettings (estimated 0 rows)
2025-06-30 17:10:20,929 - INFO - Executing: Sample data from cam.AdvancedSettings
2025-06-30 17:10:20,935 - INFO - Executing: Columns for cam.AdvancedSettings
2025-06-30 17:10:20,973 - INFO - Executing: Statistics for cam.AdvancedSettings.AdvancedSettingId
2025-06-30 17:10:20,978 - INFO - Executing: Statistics for cam.AdvancedSettings.VideoEncoderType
2025-06-30 17:10:20,984 - INFO - Executing: Statistics for cam.AdvancedSettings.VideoDecoderType
2025-06-30 17:10:20,991 - INFO - Processing table ms.BackupConfigs (estimated 0 rows)
2025-06-30 17:10:20,991 - INFO - Executing: Sample data from ms.BackupConfigs
2025-06-30 17:10:20,997 - INFO - Executing: Columns for ms.BackupConfigs
2025-06-30 17:10:21,037 - INFO - Executing: Statistics for ms.BackupConfigs.BackupConfigId
2025-06-30 17:10:21,042 - INFO - Executing: Statistics for ms.BackupConfigs.IsEnabled
2025-06-30 17:10:21,048 - INFO - Executing: Statistics for ms.BackupConfigs.IsDefault
2025-06-30 17:10:21,053 - INFO - Executing: Statistics for ms.BackupConfigs.Name
2025-06-30 17:10:21,059 - INFO - Executing: Distinct values for ms.BackupConfigs.Name
2025-06-30 17:10:21,064 - INFO - Executing: Pattern analysis for ms.BackupConfigs.Name
2025-06-30 17:10:21,071 - INFO - Executing: Statistics for ms.BackupConfigs.CronExpression
2025-06-30 17:10:21,075 - INFO - Executing: Distinct values for ms.BackupConfigs.CronExpression
2025-06-30 17:10:21,080 - INFO - Executing: Pattern analysis for ms.BackupConfigs.CronExpression
2025-06-30 17:10:21,085 - INFO - Executing: Statistics for ms.BackupConfigs.BackupPath
2025-06-30 17:10:21,090 - INFO - Executing: Distinct values for ms.BackupConfigs.BackupPath
2025-06-30 17:10:21,097 - INFO - Executing: Pattern analysis for ms.BackupConfigs.BackupPath
2025-06-30 17:10:21,103 - INFO - Executing: Statistics for ms.BackupConfigs.BackupTimestampFormat
2025-06-30 17:10:21,111 - INFO - Executing: Distinct values for ms.BackupConfigs.BackupTimestampFormat
2025-06-30 17:10:21,117 - INFO - Executing: Pattern analysis for ms.BackupConfigs.BackupTimestampFormat
2025-06-30 17:10:21,123 - INFO - Executing: Statistics for ms.BackupConfigs.BackupOptions
2025-06-30 17:10:21,129 - INFO - Executing: Distinct values for ms.BackupConfigs.BackupOptions
2025-06-30 17:10:21,135 - INFO - Executing: Pattern analysis for ms.BackupConfigs.BackupOptions
2025-06-30 17:10:21,140 - INFO - Processing table cso.BandwidthControls (estimated 0 rows)
2025-06-30 17:10:21,140 - INFO - Executing: Sample data from cso.BandwidthControls
2025-06-30 17:10:21,146 - INFO - Executing: Columns for cso.BandwidthControls
2025-06-30 17:10:21,163 - INFO - Executing: Statistics for cso.BandwidthControls.BandwidthControlId
2025-06-30 17:10:21,170 - INFO - Executing: Statistics for cso.BandwidthControls.RecordingServerId
2025-06-30 17:10:21,175 - INFO - Executing: Statistics for cso.BandwidthControls.IsOnDemandEventsChecked
2025-06-30 17:10:21,180 - INFO - Executing: Statistics for cso.BandwidthControls.StatusPeriodMs
2025-06-30 17:10:21,186 - INFO - Processing table vw.CameraViewTriggers (estimated 0 rows)
2025-06-30 17:10:21,187 - INFO - Executing: Sample data from vw.CameraViewTriggers
2025-06-30 17:10:21,192 - INFO - Executing: Columns for vw.CameraViewTriggers
2025-06-30 17:10:21,236 - INFO - Executing: Statistics for vw.CameraViewTriggers.CameraViewTriggerId
2025-06-30 17:10:21,240 - INFO - Executing: Statistics for vw.CameraViewTriggers.ViewId
2025-06-30 17:10:21,245 - INFO - Executing: Statistics for vw.CameraViewTriggers.CameraViewId
2025-06-30 17:10:21,251 - INFO - Executing: Statistics for vw.CameraViewTriggers.IoOutputId
2025-06-30 17:10:21,257 - INFO - Processing table ms.DicoveredServers (estimated 0 rows)
2025-06-30 17:10:21,257 - INFO - Executing: Sample data from ms.DicoveredServers
2025-06-30 17:10:21,264 - INFO - Executing: Columns for ms.DicoveredServers
2025-06-30 17:10:21,298 - INFO - Executing: Statistics for ms.DicoveredServers.DicoveredServerId
2025-06-30 17:10:21,303 - INFO - Executing: Statistics for ms.DicoveredServers.IpAddress
2025-06-30 17:10:21,309 - INFO - Executing: Distinct values for ms.DicoveredServers.IpAddress
2025-06-30 17:10:21,314 - INFO - Executing: Pattern analysis for ms.DicoveredServers.IpAddress
2025-06-30 17:10:21,320 - INFO - Executing: Statistics for ms.DicoveredServers.FriendlyName
2025-06-30 17:10:21,325 - INFO - Executing: Distinct values for ms.DicoveredServers.FriendlyName
2025-06-30 17:10:21,330 - INFO - Executing: Pattern analysis for ms.DicoveredServers.FriendlyName
2025-06-30 17:10:21,334 - INFO - Executing: Statistics for ms.DicoveredServers.ServerGuid
2025-06-30 17:10:21,340 - INFO - Executing: Statistics for ms.DicoveredServers.LastUpdated
2025-06-30 17:10:21,346 - INFO - Executing: Date pattern analysis for ms.DicoveredServers.LastUpdated
2025-06-30 17:10:21,350 - INFO - Processing table com.EmailServers (estimated 0 rows)
2025-06-30 17:10:21,351 - INFO - Executing: Sample data from com.EmailServers
2025-06-30 17:10:21,357 - INFO - Executing: Columns for com.EmailServers
2025-06-30 17:10:21,389 - INFO - Executing: Statistics for com.EmailServers.EmailServerId
2025-06-30 17:10:21,394 - INFO - Executing: Statistics for com.EmailServers.IsEnabled
2025-06-30 17:10:21,399 - INFO - Executing: Statistics for com.EmailServers.SmtpHostAddress
2025-06-30 17:10:21,406 - INFO - Executing: Distinct values for com.EmailServers.SmtpHostAddress
2025-06-30 17:10:21,411 - INFO - Executing: Pattern analysis for com.EmailServers.SmtpHostAddress
2025-06-30 17:10:21,415 - INFO - Executing: Statistics for com.EmailServers.SmtpHostPort
2025-06-30 17:10:21,421 - INFO - Executing: Statistics for com.EmailServers.Username
2025-06-30 17:10:21,427 - INFO - Executing: Distinct values for com.EmailServers.Username
2025-06-30 17:10:21,433 - INFO - Executing: Pattern analysis for com.EmailServers.Username
2025-06-30 17:10:21,438 - INFO - Executing: Statistics for com.EmailServers.Password
2025-06-30 17:10:21,445 - INFO - Executing: Distinct values for com.EmailServers.Password
2025-06-30 17:10:21,451 - INFO - Executing: Pattern analysis for com.EmailServers.Password
2025-06-30 17:10:21,458 - INFO - Executing: Statistics for com.EmailServers.WillUseTls
2025-06-30 17:10:21,465 - INFO - Executing: Statistics for com.EmailServers.WillAuthenticate
2025-06-30 17:10:21,472 - INFO - Executing: Statistics for com.EmailServers.SenderName
2025-06-30 17:10:21,479 - INFO - Executing: Distinct values for com.EmailServers.SenderName
2025-06-30 17:10:21,486 - INFO - Executing: Pattern analysis for com.EmailServers.SenderName
2025-06-30 17:10:21,496 - INFO - Executing: Statistics for com.EmailServers.SenderEmail
2025-06-30 17:10:21,503 - INFO - Executing: Distinct values for com.EmailServers.SenderEmail
2025-06-30 17:10:21,511 - INFO - Executing: Pattern analysis for com.EmailServers.SenderEmail
2025-06-30 17:10:21,519 - INFO - Processing table rs.EventTriggerActions (estimated 0 rows)
2025-06-30 17:10:21,519 - INFO - Executing: Sample data from rs.EventTriggerActions
2025-06-30 17:10:21,527 - INFO - Executing: Columns for rs.EventTriggerActions
2025-06-30 17:10:21,556 - INFO - Executing: Statistics for rs.EventTriggerActions.EventTriggerActionId
2025-06-30 17:10:21,572 - INFO - Executing: Statistics for rs.EventTriggerActions.EventTriggerId
2025-06-30 17:10:21,603 - INFO - Executing: Statistics for rs.EventTriggerActions.EventTriggerActionGuid
2025-06-30 17:10:21,640 - INFO - Executing: Statistics for rs.EventTriggerActions.ActionType
2025-06-30 17:10:21,659 - INFO - Executing: Statistics for rs.EventTriggerActions.ActionValue
2025-06-30 17:10:21,689 - INFO - Executing: Statistics for rs.EventTriggerActions.PriorityType
2025-06-30 17:10:21,716 - INFO - Executing: Statistics for rs.EventTriggerActions.IsPrimaryAction
2025-06-30 17:10:21,750 - INFO - Executing: Statistics for rs.EventTriggerActions.CameraId
2025-06-30 17:10:21,780 - INFO - Executing: Statistics for rs.EventTriggerActions.IOTriggerId
2025-06-30 17:10:21,843 - INFO - Executing: Statistics for rs.EventTriggerActions.VolumeId
2025-06-30 17:10:21,921 - INFO - Processing table rs.EventTriggerResponseActions (estimated 0 rows)
2025-06-30 17:10:21,923 - INFO - Executing: Sample data from rs.EventTriggerResponseActions
2025-06-30 17:10:21,947 - INFO - Executing: Columns for rs.EventTriggerResponseActions
2025-06-30 17:10:22,016 - INFO - Executing: Statistics for rs.EventTriggerResponseActions.EventTriggerResponseActionId
2025-06-30 17:10:22,045 - INFO - Executing: Statistics for rs.EventTriggerResponseActions.EventTriggerId
2025-06-30 17:10:22,069 - INFO - Executing: Statistics for rs.EventTriggerResponseActions.EventTriggerResponseId
2025-06-30 17:10:22,088 - INFO - Executing: Statistics for rs.EventTriggerResponseActions.EventTriggerActionId
2025-06-30 17:10:22,157 - INFO - Processing table rs.EventTriggerResponses (estimated 0 rows)
2025-06-30 17:10:22,163 - INFO - Executing: Sample data from rs.EventTriggerResponses
2025-06-30 17:10:22,181 - INFO - Executing: Columns for rs.EventTriggerResponses
2025-06-30 17:10:22,222 - INFO - Executing: Statistics for rs.EventTriggerResponses.EventTriggerResponseId
2025-06-30 17:10:22,241 - INFO - Executing: Statistics for rs.EventTriggerResponses.EventTriggerId
2025-06-30 17:10:22,257 - INFO - Executing: Statistics for rs.EventTriggerResponses.IsAckRequired
2025-06-30 17:10:22,273 - INFO - Executing: Statistics for rs.EventTriggerResponses.IsResetRequired
2025-06-30 17:10:22,292 - INFO - Executing: Statistics for rs.EventTriggerResponses.CanForward
2025-06-30 17:10:22,310 - INFO - Processing table rs.EventTriggers (estimated 0 rows)
2025-06-30 17:10:22,311 - INFO - Executing: Sample data from rs.EventTriggers
2025-06-30 17:10:22,327 - INFO - Executing: Columns for rs.EventTriggers
2025-06-30 17:10:22,374 - INFO - Executing: Statistics for rs.EventTriggers.EventTriggerId
2025-06-30 17:10:22,415 - INFO - Executing: Statistics for rs.EventTriggers.RecordingServerId
2025-06-30 17:10:22,442 - INFO - Executing: Statistics for rs.EventTriggers.EventTriggerResponseId
2025-06-30 17:10:22,481 - INFO - Executing: Statistics for rs.EventTriggers.EventTriggerGuid
2025-06-30 17:10:22,535 - INFO - Executing: Statistics for rs.EventTriggers.IsEnabled
2025-06-30 17:10:22,567 - INFO - Executing: Statistics for rs.EventTriggers.Name
2025-06-30 17:10:22,593 - INFO - Executing: Distinct values for rs.EventTriggers.Name
2025-06-30 17:10:22,673 - INFO - Executing: Pattern analysis for rs.EventTriggers.Name
2025-06-30 17:10:22,699 - INFO - Executing: Statistics for rs.EventTriggers.SetIOTrigger
2025-06-30 17:10:22,741 - INFO - Executing: Statistics for rs.EventTriggers.PriorityType
2025-06-30 17:10:22,776 - INFO - Executing: Statistics for rs.EventTriggers.Note
2025-06-30 17:10:22,802 - INFO - Executing: Distinct values for rs.EventTriggers.Note
2025-06-30 17:10:22,885 - INFO - Executing: Pattern analysis for rs.EventTriggers.Note
2025-06-30 17:10:22,895 - INFO - Processing table shl.EventTriggerSchedules (estimated 0 rows)
2025-06-30 17:10:22,896 - INFO - Executing: Sample data from shl.EventTriggerSchedules
2025-06-30 17:10:22,913 - INFO - Executing: Columns for shl.EventTriggerSchedules
2025-06-30 17:10:22,954 - INFO - Executing: Statistics for shl.EventTriggerSchedules.EventTriggerScheduleId
2025-06-30 17:10:22,982 - INFO - Executing: Statistics for shl.EventTriggerSchedules.ScheduleId
2025-06-30 17:10:23,008 - INFO - Executing: Statistics for shl.EventTriggerSchedules.EventTriggerId
2025-06-30 17:10:23,074 - INFO - Processing table rs.EventTriggerSources (estimated 0 rows)
2025-06-30 17:10:23,074 - INFO - Executing: Sample data from rs.EventTriggerSources
2025-06-30 17:10:23,081 - INFO - Executing: Columns for rs.EventTriggerSources
2025-06-30 17:10:23,154 - INFO - Executing: Statistics for rs.EventTriggerSources.EventTriggerSourceId
2025-06-30 17:10:23,185 - INFO - Executing: Statistics for rs.EventTriggerSources.EventTriggerId
2025-06-30 17:10:23,218 - INFO - Executing: Statistics for rs.EventTriggerSources.EventSourceGuid
2025-06-30 17:10:23,242 - INFO - Executing: Statistics for rs.EventTriggerSources.EventType
2025-06-30 17:10:23,266 - INFO - Executing: Statistics for rs.EventTriggerSources.EventValue
2025-06-30 17:10:23,303 - INFO - Executing: Statistics for rs.EventTriggerSources.IsPrimaryEvent
2025-06-30 17:10:23,324 - INFO - Executing: Statistics for rs.EventTriggerSources.CameraId
2025-06-30 17:10:23,337 - INFO - Executing: Statistics for rs.EventTriggerSources.IOTriggerId
2025-06-30 17:10:23,364 - INFO - Executing: Statistics for rs.EventTriggerSources.VolumeId
2025-06-30 17:10:23,384 - INFO - Executing: Statistics for rs.EventTriggerSources.UserId
2025-06-30 17:10:23,423 - INFO - Processing table ms.FederatedChildren (estimated 0 rows)
2025-06-30 17:10:23,424 - INFO - Executing: Sample data from ms.FederatedChildren
2025-06-30 17:10:23,439 - INFO - Executing: Columns for ms.FederatedChildren
2025-06-30 17:10:23,486 - INFO - Executing: Statistics for ms.FederatedChildren.Guid
2025-06-30 17:10:23,507 - INFO - Executing: Statistics for ms.FederatedChildren.Name
2025-06-30 17:10:23,527 - INFO - Executing: Distinct values for ms.FederatedChildren.Name
2025-06-30 17:10:23,563 - INFO - Executing: Pattern analysis for ms.FederatedChildren.Name
2025-06-30 17:10:23,595 - INFO - Executing: Statistics for ms.FederatedChildren.Version
2025-06-30 17:10:23,614 - INFO - Executing: Distinct values for ms.FederatedChildren.Version
2025-06-30 17:10:23,647 - INFO - Executing: Pattern analysis for ms.FederatedChildren.Version
2025-06-30 17:10:23,672 - INFO - Executing: Statistics for ms.FederatedChildren.UseTls
2025-06-30 17:10:23,711 - INFO - Executing: Statistics for ms.FederatedChildren.HostAddress
2025-06-30 17:10:23,726 - INFO - Executing: Distinct values for ms.FederatedChildren.HostAddress
2025-06-30 17:10:23,734 - INFO - Executing: Pattern analysis for ms.FederatedChildren.HostAddress
2025-06-30 17:10:23,762 - INFO - Executing: Statistics for ms.FederatedChildren.Port
2025-06-30 17:10:23,776 - INFO - Executing: Statistics for ms.FederatedChildren.Identity
2025-06-30 17:10:23,824 - INFO - Executing: Distinct values for ms.FederatedChildren.Identity
2025-06-30 17:10:23,860 - INFO - Executing: Pattern analysis for ms.FederatedChildren.Identity
2025-06-30 17:10:23,891 - INFO - Executing: Statistics for ms.FederatedChildren.Password
2025-06-30 17:10:23,919 - INFO - Executing: Distinct values for ms.FederatedChildren.Password
2025-06-30 17:10:24,017 - INFO - Executing: Pattern analysis for ms.FederatedChildren.Password
2025-06-30 17:10:24,087 - INFO - Executing: Statistics for ms.FederatedChildren.Note
2025-06-30 17:10:24,119 - INFO - Executing: Distinct values for ms.FederatedChildren.Note
2025-06-30 17:10:24,141 - INFO - Executing: Pattern analysis for ms.FederatedChildren.Note
2025-06-30 17:10:24,275 - INFO - Executing: Statistics for ms.FederatedChildren.WriteToken
2025-06-30 17:10:24,303 - INFO - Executing: Statistics for ms.FederatedChildren.RecordingServersCount
2025-06-30 17:10:24,334 - INFO - Executing: Statistics for ms.FederatedChildren.CameraLicensesPurchased
2025-06-30 17:10:24,390 - INFO - Executing: Statistics for ms.FederatedChildren.CameraLicensesUsed
2025-06-30 17:10:24,482 - INFO - Processing table ms.FederatedParent (estimated 0 rows)
2025-06-30 17:10:24,486 - INFO - Executing: Sample data from ms.FederatedParent
2025-06-30 17:10:24,561 - INFO - Executing: Columns for ms.FederatedParent
2025-06-30 17:10:24,645 - INFO - Executing: Statistics for ms.FederatedParent.Guid
2025-06-30 17:10:24,677 - INFO - Executing: Statistics for ms.FederatedParent.Name
2025-06-30 17:10:24,700 - INFO - Executing: Distinct values for ms.FederatedParent.Name
2025-06-30 17:10:24,733 - INFO - Executing: Pattern analysis for ms.FederatedParent.Name
2025-06-30 17:10:24,783 - INFO - Executing: Statistics for ms.FederatedParent.Version
2025-06-30 17:10:24,829 - INFO - Executing: Distinct values for ms.FederatedParent.Version
2025-06-30 17:10:24,852 - INFO - Executing: Pattern analysis for ms.FederatedParent.Version
2025-06-30 17:10:24,894 - INFO - Executing: Statistics for ms.FederatedParent.UseTls
2025-06-30 17:10:24,935 - INFO - Executing: Statistics for ms.FederatedParent.HostAddress
2025-06-30 17:10:24,952 - INFO - Executing: Distinct values for ms.FederatedParent.HostAddress
2025-06-30 17:10:24,980 - INFO - Executing: Pattern analysis for ms.FederatedParent.HostAddress
2025-06-30 17:10:25,036 - INFO - Executing: Statistics for ms.FederatedParent.Port
2025-06-30 17:10:25,090 - INFO - Executing: Statistics for ms.FederatedParent.Note
2025-06-30 17:10:25,101 - INFO - Executing: Distinct values for ms.FederatedParent.Note
2025-06-30 17:10:25,150 - INFO - Executing: Pattern analysis for ms.FederatedParent.Note
2025-06-30 17:10:25,187 - INFO - Executing: Statistics for ms.FederatedParent.WriteToken
2025-06-30 17:10:25,203 - INFO - Processing table ms.FederatedSiblings (estimated 0 rows)
2025-06-30 17:10:25,203 - INFO - Executing: Sample data from ms.FederatedSiblings
2025-06-30 17:10:25,217 - INFO - Executing: Columns for ms.FederatedSiblings
2025-06-30 17:10:25,286 - INFO - Executing: Statistics for ms.FederatedSiblings.Guid
2025-06-30 17:10:25,298 - INFO - Executing: Statistics for ms.FederatedSiblings.Name
2025-06-30 17:10:25,307 - INFO - Executing: Distinct values for ms.FederatedSiblings.Name
2025-06-30 17:10:25,325 - INFO - Executing: Pattern analysis for ms.FederatedSiblings.Name
2025-06-30 17:10:25,344 - INFO - Executing: Statistics for ms.FederatedSiblings.Version
2025-06-30 17:10:25,377 - INFO - Executing: Distinct values for ms.FederatedSiblings.Version
2025-06-30 17:10:25,398 - INFO - Executing: Pattern analysis for ms.FederatedSiblings.Version
2025-06-30 17:10:25,422 - INFO - Executing: Statistics for ms.FederatedSiblings.UseTls
2025-06-30 17:10:25,445 - INFO - Executing: Statistics for ms.FederatedSiblings.HostAddress
2025-06-30 17:10:25,475 - INFO - Executing: Distinct values for ms.FederatedSiblings.HostAddress
2025-06-30 17:10:25,567 - INFO - Executing: Pattern analysis for ms.FederatedSiblings.HostAddress
2025-06-30 17:10:25,675 - INFO - Executing: Statistics for ms.FederatedSiblings.Port
2025-06-30 17:10:25,694 - INFO - Processing table ms.FederationSettings (estimated 0 rows)
2025-06-30 17:10:25,695 - INFO - Executing: Sample data from ms.FederationSettings
2025-06-30 17:10:25,736 - INFO - Executing: Columns for ms.FederationSettings
2025-06-30 17:10:25,803 - INFO - Executing: Statistics for ms.FederationSettings.IsEnabled
2025-06-30 17:10:25,830 - INFO - Processing table vw.FixedTiles (estimated 0 rows)
2025-06-30 17:10:25,831 - INFO - Executing: Sample data from vw.FixedTiles
2025-06-30 17:10:25,889 - INFO - Executing: Columns for vw.FixedTiles
2025-06-30 17:10:25,933 - INFO - Executing: Statistics for vw.FixedTiles.FixedTileId
2025-06-30 17:10:25,947 - INFO - Executing: Statistics for vw.FixedTiles.ViewId
2025-06-30 17:10:25,961 - INFO - Executing: Statistics for vw.FixedTiles.TileId
2025-06-30 17:10:25,992 - INFO - Executing: Statistics for vw.FixedTiles.PositionX
2025-06-30 17:10:26,017 - INFO - Executing: Statistics for vw.FixedTiles.PositionY
2025-06-30 17:10:26,051 - INFO - Executing: Statistics for vw.FixedTiles.Width
2025-06-30 17:10:26,073 - INFO - Executing: Statistics for vw.FixedTiles.Height
2025-06-30 17:10:26,096 - INFO - Processing table ms.ImportRecordingServerQueue (estimated 0 rows)
2025-06-30 17:10:26,097 - INFO - Executing: Sample data from ms.ImportRecordingServerQueue
2025-06-30 17:10:26,116 - INFO - Executing: Columns for ms.ImportRecordingServerQueue
2025-06-30 17:10:26,163 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.ImportRecordingServerQueueId
2025-06-30 17:10:26,207 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.FriendlyName
2025-06-30 17:10:26,224 - INFO - Executing: Distinct values for ms.ImportRecordingServerQueue.FriendlyName
2025-06-30 17:10:26,235 - INFO - Executing: Pattern analysis for ms.ImportRecordingServerQueue.FriendlyName
2025-06-30 17:10:26,248 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.HostAddress
2025-06-30 17:10:26,257 - INFO - Executing: Distinct values for ms.ImportRecordingServerQueue.HostAddress
2025-06-30 17:10:26,276 - INFO - Executing: Pattern analysis for ms.ImportRecordingServerQueue.HostAddress
2025-06-30 17:10:26,288 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.SvrCtrlPortNo
2025-06-30 17:10:26,301 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.RestPortNo
2025-06-30 17:10:26,311 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.UseTls
2025-06-30 17:10:26,322 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.Username
2025-06-30 17:10:26,337 - INFO - Executing: Distinct values for ms.ImportRecordingServerQueue.Username
2025-06-30 17:10:26,347 - INFO - Executing: Pattern analysis for ms.ImportRecordingServerQueue.Username
2025-06-30 17:10:26,362 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.Password
2025-06-30 17:10:26,381 - INFO - Executing: Distinct values for ms.ImportRecordingServerQueue.Password
2025-06-30 17:10:26,407 - INFO - Executing: Pattern analysis for ms.ImportRecordingServerQueue.Password
2025-06-30 17:10:26,419 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.AutoProvision
2025-06-30 17:10:26,432 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.ConnectionTimeoutMs
2025-06-30 17:10:26,446 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.AddUserId
2025-06-30 17:10:26,452 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.EnqueuedAt
2025-06-30 17:10:26,460 - INFO - Executing: Date pattern analysis for ms.ImportRecordingServerQueue.EnqueuedAt
2025-06-30 17:10:26,468 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.RegionHierarchyId
2025-06-30 17:10:26,476 - INFO - Executing: Statistics for ms.ImportRecordingServerQueue.PostActions
2025-06-30 17:10:26,483 - INFO - Executing: Distinct values for ms.ImportRecordingServerQueue.PostActions
2025-06-30 17:10:26,492 - INFO - Executing: Pattern analysis for ms.ImportRecordingServerQueue.PostActions
2025-06-30 17:10:26,504 - INFO - Processing table ms.ImportServerConfigQueue (estimated 0 rows)
2025-06-30 17:10:26,504 - INFO - Executing: Sample data from ms.ImportServerConfigQueue
2025-06-30 17:10:26,513 - INFO - Executing: Columns for ms.ImportServerConfigQueue
2025-06-30 17:10:26,552 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.ImportServerConfigQueueGuid
2025-06-30 17:10:26,559 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.AutoProvision
2025-06-30 17:10:26,568 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.FriendlyName
2025-06-30 17:10:26,575 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.FriendlyName
2025-06-30 17:10:26,584 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.FriendlyName
2025-06-30 17:10:26,591 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.HostAddress
2025-06-30 17:10:26,597 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.HostAddress
2025-06-30 17:10:26,605 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.HostAddress
2025-06-30 17:10:26,614 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.ServerGuid
2025-06-30 17:10:26,621 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.Version
2025-06-30 17:10:26,628 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.Version
2025-06-30 17:10:26,636 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.Version
2025-06-30 17:10:26,644 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.UseTls
2025-06-30 17:10:26,650 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.Username
2025-06-30 17:10:26,657 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.Username
2025-06-30 17:10:26,663 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.Username
2025-06-30 17:10:26,671 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.Password
2025-06-30 17:10:26,679 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.Password
2025-06-30 17:10:26,684 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.Password
2025-06-30 17:10:26,693 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.Platform
2025-06-30 17:10:26,701 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.Platform
2025-06-30 17:10:26,707 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.Platform
2025-06-30 17:10:26,715 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.SerialNo
2025-06-30 17:10:26,721 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.SerialNo
2025-06-30 17:10:26,728 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.SerialNo
2025-06-30 17:10:26,736 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.EnqueuedAt
2025-06-30 17:10:26,743 - INFO - Executing: Date pattern analysis for ms.ImportServerConfigQueue.EnqueuedAt
2025-06-30 17:10:26,750 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.ServerTimeZoneInfo
2025-06-30 17:10:26,756 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.ServerTimeZoneInfo
2025-06-30 17:10:26,762 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.ServerTimeZoneInfo
2025-06-30 17:10:26,771 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.EnvironmentInfo
2025-06-30 17:10:26,778 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.EnvironmentInfo
2025-06-30 17:10:26,784 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.EnvironmentInfo
2025-06-30 17:10:26,792 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.ServerConfigRoot
2025-06-30 17:10:26,800 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.ServerConfigRoot
2025-06-30 17:10:26,806 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.ServerConfigRoot
2025-06-30 17:10:26,814 - INFO - Executing: Statistics for ms.ImportServerConfigQueue.LicenseInfo
2025-06-30 17:10:26,820 - INFO - Executing: Distinct values for ms.ImportServerConfigQueue.LicenseInfo
2025-06-30 17:10:26,828 - INFO - Executing: Pattern analysis for ms.ImportServerConfigQueue.LicenseInfo
2025-06-30 17:10:26,836 - INFO - Generating AI-specific insights and metadata...
2025-06-30 17:10:26,837 - INFO - Executing: Potential table relationships
2025-06-30 17:11:58,528 - INFO - Analyzing database structure for AI annotations...
2025-06-30 17:11:58,528 - INFO - Executing: Table metadata for AI analysis
2025-06-30 17:11:58,533 - INFO - Executing: Common join patterns
2025-06-30 17:11:58,686 - INFO - Database connection closed
