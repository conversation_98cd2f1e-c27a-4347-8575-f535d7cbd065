#!/usr/bin/env python3
"""
Setup script for Comprehensive Schema Export Tool
Installs dependencies and validates environment
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install requirements: {e}")
        return False

def check_odbc_drivers():
    """Check available ODBC drivers"""
    print("\nChecking ODBC drivers...")
    try:
        import pyodbc
        drivers = pyodbc.drivers()
        sql_drivers = [d for d in drivers if 'SQL Server' in d]
        
        if sql_drivers:
            print("✓ Available SQL Server ODBC drivers:")
            for driver in sql_drivers:
                print(f"  - {driver}")
        else:
            print("✗ No SQL Server ODBC drivers found")
            print("Please install SQL Server ODBC drivers from:")
            print("https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server")
        
        return len(sql_drivers) > 0
    except ImportError:
        print("✗ pyodbc not available")
        return False

def create_env_template():
    """Create environment variable template"""
    env_template = """# Environment Variables Template for Schema Export
# Copy this to .env or set as system environment variables

# Database Connection Settings
DB_SERVER=your-server\\instance-name
DB_DATABASE=your-database-name
DB_USERNAME=your-username
DB_PASSWORD=your-password

# Optional: Export Settings
EXPORT_SAMPLE_SIZE=10
EXPORT_MAX_DISTINCT_VALUES=50
EXPORT_BACKUP_TO_DESKTOP=true
"""
    
    with open("env_template.txt", "w") as f:
        f.write(env_template)
    
    print("✓ Created env_template.txt for environment variables")

def main():
    """Main setup function"""
    print("=" * 60)
    print("COMPREHENSIVE SCHEMA EXPORT TOOL - SETUP")
    print("=" * 60)
    
    # Install requirements
    if not install_requirements():
        print("\nSetup failed. Please install requirements manually:")
        print("pip install pyodbc")
        return False
    
    # Check ODBC drivers
    drivers_ok = check_odbc_drivers()
    
    # Create environment template
    create_env_template()
    
    print("\n" + "=" * 60)
    if drivers_ok:
        print("✓ SETUP COMPLETED SUCCESSFULLY")
        print("\nNext steps:")
        print("1. Configure database connection in env_template.txt")
        print("2. Set environment variables or update script configuration")
        print("3. Run: python comprehensive_schema_export.py")
        print("4. Or try examples: python schema_export_example.py")
    else:
        print("⚠ SETUP COMPLETED WITH WARNINGS")
        print("\nPlease install SQL Server ODBC drivers before using the tool.")
    
    print("=" * 60)
    return drivers_ok

if __name__ == "__main__":
    main()
