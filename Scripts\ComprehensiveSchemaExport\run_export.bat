@echo off
REM Comprehensive Schema Export Tool - Quick Run Script
REM This script runs the schema export with default settings

echo ========================================
echo COMPREHENSIVE SCHEMA EXPORT TOOL
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if pyodbc is installed
python -c "import pyodbc" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install requirements
        pause
        exit /b 1
    )
)

REM Run the schema export
echo Running comprehensive schema export...
echo.
python comprehensive_schema_export.py

REM Check if export was successful
if errorlevel 1 (
    echo.
    echo ERROR: Schema export failed
    echo Check the log file for details: schema_export.log
) else (
    echo.
    echo SUCCESS: Schema export completed
    echo Check the generated files in this directory
)

echo.
pause
